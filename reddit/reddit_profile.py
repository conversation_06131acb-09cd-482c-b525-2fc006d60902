import requests

def get_reddit_profile(token):
    if not token:
        return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': 'No token provided'}

    user_info_url = 'https://oauth.reddit.com/api/v1/me'
    user_info_headers = {
        'Authorization': f'bearer {token}',
        'User-Agent': 'MyBot/0.0.1'
    }
    user_info_response = requests.get(user_info_url, headers=user_info_headers)

    try:
        if user_info_response.status_code != 200:
            return {'error': f'Error fetching user info: {user_info_response.text}'}

        profile_data = user_info_response.json()
        username = profile_data.get('name', '')
        
        return {
            'profile_url': f'https://www.reddit.com/user/{username}', 
            'username': username,
            'name': profile_data.get('subreddit', {}).get('display_name', ''), 
            'profile_image': profile_data.get('icon_img', '')
        }
    except Exception as e:
        return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': f'Error: {e}'}
    

def get_reddit_profile_multiple(token):
    if not token:
        return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': 'No token provided'}

    user_info_url = 'https://oauth.reddit.com/api/v1/me'
    user_info_headers = {
        'Authorization': f'bearer {token}',
        'User-Agent': 'MyBot/0.0.1'
    }
    user_info_response = requests.get(user_info_url, headers=user_info_headers)

    try:
        if user_info_response.status_code != 200:
            return {'error': f'Error fetching user info: {user_info_response.text}'}

        profile_data = user_info_response.json()
        username = profile_data.get('name', '')
        
        return  username
    except Exception as e:
        return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': f'Error: {e}'}
