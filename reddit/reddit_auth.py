import requests

CLIENT_ID = 'CMDfsM1CRdMYbxbrdydB_Q'
CLIENT_SECRET = '4k8LDDvt3OjM8g2JV6BOnt8_NML9_Q'
# REDIRECT_URI = 'https://staging.yooii.com/api/reddit/'
# REDIRECT_URI = 'https://staging.flowkar.com/api/reddit/'
REDIRECT_URI = 'https://api.flowkar.com/api/reddit/'
STATE = 'random_state_string'

SCOPE = 'identity edit flair history modconfig modflair modlog modposts modwiki mysubreddits privatemessages read report save submit subscribe vote wikiedit wikiread'


def reddit_auth_url(user_id):
    auth_url = "https://www.reddit.com/api/v1/authorize"

    params = {
        'client_id': CLIENT_ID,
        'response_type': 'code',
        'state': STATE,
        'redirect_uri': REDIRECT_URI,
        'duration': 'permanent',  # Temporary ne badle Permanent kariyu
        'scope': SCOPE,
        'state':user_id
    }

    request_url = requests.Request(
        'GET', auth_url, params=params).prepare().url
    return request_url


def reddit_authorise(token):
    token_url = "https://www.reddit.com/api/v1/access_token"
    auth = requests.auth.HTTPBasicAuth(CLIENT_ID, CLIENT_SECRET)

    data = {
        'grant_type': 'authorization_code',
        'code': token,
        'redirect_uri': REDIRECT_URI
    }

    headers = {'User-Agent': 'MyBot/0.0.1'}

    response = requests.post(token_url, auth=auth, data=data, headers=headers)
    token_data = response.json()
    access_token = token_data['access_token']
    return access_token

def upload_photo_to_reddit(access_token, title, image_url):
    user_info_url = 'https://oauth.reddit.com/api/v1/me'
    user_info_headers = {
        'Authorization': f'bearer {access_token}',
        'User-Agent': 'MyBot/0.0.1'
    }

    user_info_response = requests.get(user_info_url, headers=user_info_headers)
    
    if user_info_response.status_code != 200:
        raise Exception(f"Error fetching user info: {user_info_response.status_code} - {user_info_response.text}")

    data = user_info_response.json()
    subreddit = data['subreddit']['display_name']
    url = f'https://oauth.reddit.com/r/{subreddit}/api/submit'
    
    headers = {
        'Authorization': f'bearer {access_token}',
        'User-Agent': 'MyBot/0.1'
    }

    # Prepare the data
    data = {
        'title': title,
        'sr': subreddit,
        'kind': 'link',  # Use 'link' for URL submissions
        'url': image_url  # Use the URL of the hosted image
    }
    print(data)

    response = requests.post(url, headers=headers, data=data)
    print(response.json())
    if response.status_code == 200:
        return response.json() , True
    else:
        print('Failed to upload photo:', response.status_code, response.json())
        return 'Failed Upload',False


# access_token = '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
# image ="https://staging.yooii.com/media/post_files/IMG_20240822_110132.jpg"
# test = upload_photo_to_reddit(access_token, 'Test From API', image)
# print(test)