import os
from instagrapi import Client
from instagrapi.exceptions import Bad<PERSON><PERSON><PERSON>,ProxyAddressIsBlocked,BadCredentials,Unknown<PERSON><PERSON><PERSON>
def insta_login(username,password,file_dir,email):   
    client = Client() 
    json_file_path = os.path.join(file_dir, f"{email}.json")

    try:
       
        if not os.path.exists(file_dir):
            os.makedirs(file_dir)  
        try:
            data = client.login(username, password)
            client.dump_settings(json_file_path)
            return True
        except (BadPassword,BadCredentials, ProxyAddressIsBlocked,UnknownError):
            return False

    except OSError as e:
        raise OSError(f"An error occurred while creating the file: {e}") from e 
    except ValueError as e:
        raise ValueError(f"Invalid directory path: {e}") from e  
    



