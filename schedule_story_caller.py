import requests
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('flowkar_api_calls.log'),
        logging.StreamHandler()
    ]
)

def check_story():
    """Call the check-story endpoint and log the response"""
    try:
        response = requests.get('https://api.flowkar.com/api/check-story/')
        logging.info(f"Check Story Response - Status: {response.status_code}, Body: {response.text}")
        return response
    except Exception as e:
        logging.error(f"Error calling check-story endpoint: {str(e)}")
        return None

def upload_scheduled_post():
    """Call the upload-scheduled-post endpoint and log the response"""
    try:
        response = requests.get('https://api.flowkar.com/api/upload-scheduled-post/')
        logging.info(f"Upload Scheduled Post Response - Status: {response.status_code}, Body: {response.text}")
        return response
    except Exception as e:
        logging.error(f"Error calling upload-scheduled-post endpoint: {str(e)}")
        return None

def main():
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logging.info(f"Running API calls at {current_time}")
    
    check_story()
    upload_scheduled_post()

if __name__ == "__main__":
    main()
