import requests


def get_thread_likes_and_comments(post_id, access_token):
    url = f"https://graph.threads.net/v1.0/{post_id}/insights"
    params = {
        'metric': 'likes,views,replies,reposts,shares',
        'access_token': access_token,
    }
    response = requests.get(url, params=params)
    data = response.json()
    if response.status_code == 200:
        likes = data['data'][0]['values'][0]['value']
        views = data['data'][1]['values'][0]['value']
        replies = data['data'][2]['values'][0]['value']
        reposts = data['data'][3]['values'][0]['value']
        shares = data['data'][4]['values'][0]['value']
        return likes, replies
    else:
        return 0, 0
    
def get_post_share_threads_url(token,post_id):
    url = f"https://graph.threads.net/v1.0/{post_id}?fields=permalink"

    headers = {
        'Authorization': f'Bearer {token}'
    }

    response = requests.request("GET", url, headers=headers)
    if response.status_code == 200:
        return True,response.json()['permalink']
    else:
        return False,''
