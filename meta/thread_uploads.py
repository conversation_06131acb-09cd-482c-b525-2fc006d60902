import time
import requests

def upload_images_to_thread(user_id, access_token, image_urls,caption=None):
    is_carousel_item=False
    if len(image_urls) <= 1:
        url = f'https://graph.threads.net/v1.0/{user_id}/threads'
        params = {
            'media_type':'IMAGE',
            'image_url': image_urls[0],
            'access_token': access_token,
            'text':caption,
            'is_carousel_item':is_carousel_item,
        }
        
        response = requests.post(url, params=params)
        if response.status_code == 200:
            response_data = response.json()
            publish_single_url = f'https://graph.threads.net/v1.0/{user_id}/threads_publish'
            single_publish_params = {
                'creation_id':response_data['id'],
                'access_token':access_token
            }
            post_single_publish = requests.post(publish_single_url,params=single_publish_params)
            post_single_response = post_single_publish.json()
            if post_single_publish.status_code == 200:
                return True , post_single_response['id']
            else:
                return False, 0
        else:
            print("Error uploading image:", response.json())
            return False, 0
    else:  
        media_ids = []

        # Upload each image
        for image_url in image_urls:
            # Step 1: Upload the image
            url = f'https://graph.threads.net/v1.0/{user_id}/threads'
            data = {
                'media_type':'IMAGE',
                'image_url': image_url,
                'access_token': access_token,
                'is_carousel_item':is_carousel_item,
            }
            
            response = requests.post(url, params=data)
            if response.status_code == 200:
                response_data = response.json()
                media_ids.append(response_data['id'])  # Collect media IDs for the carousel
            else:
                print("Error uploading image:", response.json())
                return False, 0

        # Step 2: Create a carousel container
        carousel_url = f'https://graph.threads.net/v1.0/{user_id}/threads'
        carousel_data = {
            'media_type': 'CAROUSEL',
            'children': ','.join(media_ids),  # List of uploaded image media IDs
            'access_token': access_token,
            'text':caption
        }
        
        carousel_response = requests.post(carousel_url, params=carousel_data)
        
        if carousel_response.status_code == 200:
            carousel_response_data = carousel_response.json()
            creation_id = carousel_response_data['id']
        else:
            print("Error creating carousel:", carousel_response.json())
            return False, 0

        # Step 3: Publish the carousel
        publish_url = f'https://graph.threads.net/v1.0/{user_id}/threads_publish'
        publish_params = {
            'creation_id': creation_id,
            'access_token': access_token
        }

        publish_response = requests.post(publish_url, params=publish_params)

        if publish_response.status_code == 200:
            publish_response_data = publish_response.json()
            return True, publish_response_data['id']
        else:
            print("Error publishing post:", publish_response.json())
            return False, 0
    

def upload_and_publish_thread_video(user_id, access_token, video_url, caption=None):
    # Step 1: Upload the video
    url = f'https://graph.threads.net/v1.0/{user_id}/threads'
    data = {
        'video_url': video_url,
        'text': caption,
        'access_token': access_token,
        'media_type': 'VIDEO'  # For video posts (can also be 'VIDEO' if it's a normal video post)
    }

    upload_request = requests.post(url, params=data)
    
    if upload_request.status_code == 200:
        upload_response = upload_request.json()
        creation_id = upload_response['id']  # Get the creation ID for the uploaded video
        
        # Wait for the media to be processed by Instagram

        # Step 2: Check the media status and wait until it's ready
        media_status_url = f'https://graph.threads.net/{creation_id}'
        status_data = {
            'access_token': access_token
        }

        # Retry until media is ready or a timeout occurs
        retries = 10  # Set a retry limit
        for _ in range(retries):
            status_request = requests.get(media_status_url, params=status_data)
            if status_request.status_code == 200:
                status_response = status_request.json()
                if status_response.get('status') == 'FINISHED':  # Check if the media is ready
                    print(f"Media {creation_id} is ready to be published.")
                    break
            print(f"Waiting for media {creation_id} to be processed... (Retry {_ + 1} of {retries})")
            time.sleep(6)  # Wait 15 seconds before checking again
        else:
            # If retries exhausted, fail the process
            print("Failed to upload media, retry limit reached.")
            return False, 0
        
        # Step 3: Publish the video
        publish_url = f'https://graph.threads.net/v1.0/{user_id}/threads_publish'
        publish_data = {
            'creation_id': creation_id,
            'access_token': access_token
        }

        publish_request = requests.post(publish_url, params=publish_data)
        
        # Check if the response is valid and not empty
        if publish_request.status_code == 200:
            publish_response = publish_request.json()
            print(f"Video published successfully with ID: {publish_response['id']}")
            return True, publish_response['id']  # Return the ID of the published video
        else:
            print(f"Error publishing post: {publish_request.status_code} - {publish_request.text}")
            return False, 0
    else:
        # If the upload failed, log the error and response content
        print(f"Error uploading video: {upload_request.status_code} - {upload_request.text}")
        return False, 0


# user_id = 9036131453110984
# access_token = 'THQWJXY0JrejB2N0J4UXlXLXc2YndJb3RId3RRcVBjZAV9UeFNOVjRCYmkyTk5ZAZAXJYTnlqM2N6czlBYnNsN0NJMTVZAcDgzd09sQmpIdmlCWDMyUFlBMDRVdkJaLXZAhZAmhhUDgtQ2lPdWdwajZAuMG52bmktS201WFVjN3cZD'
# video_url = 'https://staging.yooii.com/media/post_files/compressed_VID-20241024-WA0001.mp4'
# caption = ' Jai Hanuman Dada 2'
# print(upload_and_publish_thread_video(user_id,access_token,video_url,caption))