import requests


THREADS_CLIENT_ID = 2870871319739967
# THREADS_REDIRECT_URI = 'https://staging.yooii.com/api/threads'
# THREADS_REDIRECT_URI = 'https://staging.flowkar.com/api/threads'
# THREADS_REDIRECT_URI = 'https://dev.flowkar.com/api/threads'
THREADS_REDIRECT_URI = 'https://api.flowkar.com/api/threads'
THREADS_CLIENT_SECRET = 'b9a6ce7ca0f849aae1ddf328885b9905'
def generate_threads_url(user_id):
        auth_url = (
            f'https://threads.net/oauth/authorize?'
            f"client_id={THREADS_CLIENT_ID}&"
            f"redirect_uri={THREADS_REDIRECT_URI}&"
            f"scope=threads_basic,threads_content_publish,threads_manage_insights&"
            f"response_type=code&"
            f"state={user_id}"
        )

        return auth_url


def threads_after_auth(code):
    url = 'https://graph.threads.net/oauth/access_token'
    body = {
        'client_id':THREADS_CLIENT_ID,
        'client_secret':THREADS_CLIENT_SECRET,
        'code':code,
        'grant_type':'authorization_code',
        'redirect_uri':THREADS_REDIRECT_URI

    }
    post_token = requests.post(url,data=body)
    post_token_responce = post_token.json()
    if post_token.status_code == 200:
        access_token = post_token_responce['access_token']
        long_lived_url = f'https://graph.threads.net/access_token'
        print(long_lived_url)
        long_lived_params = {
             'client_secret':THREADS_CLIENT_SECRET,
             'grant_type':'th_exchange_token',
             'access_token':post_token_responce['access_token']
        }
        get_long_lived_token = requests.get(long_lived_url,params=long_lived_params)
        get_long_lived_token_response = get_long_lived_token.json()
        if get_long_lived_token.status_code == 200:
            return get_long_lived_token_response['access_token'],post_token_responce['user_id']
        

def get_threads_user_info(access_token):
    """
    Fetch the Threads user's username and profile image.

    :param access_token: The access token obtained after user authorization.
    :param user_id: The Threads user's Instagram ID.
    :return: A dictionary containing username and profile picture URL.
    """
    url = f"https://graph.threads.net/me"
    params = {
        "fields": "id,username,threads_profile_picture_url",
        "access_token": access_token
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        user_info = response.json()
        username = user_info.get("username")
        return {
            "username": user_info.get("username"),
            "profile_picture_url": user_info.get("threads_profile_picture_url"),
            "threads_profile_url": f"https://www.threads.net/@{username}" if username else None
        }
    except requests.exceptions.RequestException as e:
        print(f"Error fetching user info: {e}")
        return None


def get_threads_user_profile_info(access_token,user_id):
    """
    Fetch the Threads user's username and profile image.

    :param access_token: The access token obtained after user authorization.
    :param user_id: The Threads user's Instagram ID.
    :return: A dictionary containing username and profile picture URL.
    """
    url = f"https://graph.threads.net/{user_id}"
    media_url = f"https://graph.threads.net/{user_id}/threads"
    params = {
        "fields": "id,username,threads_profile_picture_url,threads_biography,name",
        "access_token": access_token
    }
    media_params = {
        "fields":"media_url",
        "access_token": access_token
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        user_info = response.json()

        media_response = requests.get(media_url, params=media_params)
        media_response.raise_for_status()
        media_info = media_response.json()
        return {
            "username": user_info.get("username"),
            "profile_picture_url": user_info.get("threads_profile_picture_url"),
            "name": user_info.get("name"),
            "bio": user_info.get("threads_biography"),
            "media": media_info['data'][0]['media_url'] if media_info['data'] else '',
        }
    except requests.exceptions.RequestException as e:
        print(f"Error fetching user info: {e}")
        return None


# print(get_threads_user_profile_info('THAAozCtE1fj9BYlZACbHk3Q1dHaFd4OUdZAVVNteWs0MzBzSVlMS1BxVUM1d01yRkhnVGVDcFA3T0JvTmxZAeXhab0U5cS1va2tyMkRVZAjJuTWMtQUdPQzk3em1ES0NLbXhYSVh2dUt1LWF4LXhJLUtMNGRfbmVkWGlLN1plN0NKYjNUUQZDZD',9036131453110984))