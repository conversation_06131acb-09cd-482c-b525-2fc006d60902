import secrets
from django.shortcuts import redirect
import requests


FACEBOOK_CLIENT_ID = '1510820729582053'
FACEBOOK_CLIENT_SECRET = '********************************'
# FACEBOOK_REDIRECT_URI = 'https://staging.yooii.com/api/facebook'
# FACEBOOK_REDIRECT_URI = 'https://staging.flowkar.com/api/facebook'
# FACEBOOK_REDIRECT_URI = 'https://dev.flowkar.com/api/facebook'
FACEBOOK_SCOPE = 'email,public_profile,pages_manage_posts,pages_read_engagement,pages_messaging,read_insights'
FACEBOOK_REDIRECT_URI = 'https://api.flowkar.com/api/facebook'
# FACEBOOK_SCOPE = 'email,public_profile,pages_manage_posts,pages_read_engagement,pages_messaging'
# FACEBOOK_SCOPE_DEV = 'email,public_profile,pages_manage_posts,pages_read_engagement,pages_messaging,read_insights'


def generate_facebook_auth_url(user_id):
    auth_url = (
        f"https://www.facebook.com/v21.0/dialog/oauth?"
        f"client_id={FACEBOOK_CLIENT_ID}&"
        f"redirect_uri={FACEBOOK_REDIRECT_URI}&"
        f"scope={FACEBOOK_SCOPE.replace(' ', '%20')}&"
        f"state={user_id}"
    )
    return auth_url

def generate_facebook_auth_url_dev(user_id):
    auth_url = (
        f"https://www.facebook.com/v21.0/dialog/oauth?"
        f"client_id={FACEBOOK_CLIENT_ID}&"
        f"redirect_uri={FACEBOOK_REDIRECT_URI}&"
        f"scope={FACEBOOK_SCOPE.replace(' ', '%20')}&"
        f"state={user_id}"
    )
    return auth_url


def facebook_code_to_token(code):
    try:
        url = "https://graph.facebook.com/v21.0/oauth/access_token"
        params = {
            'client_id':FACEBOOK_CLIENT_ID,
            'redirect_uri':FACEBOOK_REDIRECT_URI,
            'client_secret':FACEBOOK_CLIENT_SECRET,
            'code':code
        }
        get_token_request = requests.get(url,params=params)
        long_lived_page_token = ''
        page_id = 0
        if get_token_request.status_code == 200:
            token_response = get_token_request.json()
            access_token = token_response['access_token']

            me_url = 'https://graph.facebook.com/v21.0/me/accounts'
            me_params = {
                'access_token':access_token
            }
            get_me_request = requests.get(me_url,params=me_params)
            try:
                if get_me_request.status_code == 200:
                    me_response = get_me_request.json()
                    page_token = me_response['data'][0]['access_token']
                    page_id = me_response['data'][0]['id']

                    long_lived_params = {
                        'client_id':FACEBOOK_CLIENT_ID,
                        'client_secret':FACEBOOK_CLIENT_SECRET,
                        'fb_exchange_token':page_token,
                        'grant_type':'fb_exchange_token'
                    }

                    get_long_lived_request = requests.get(url,params=long_lived_params)
                    if get_long_lived_request.status_code == 200:
                        long_lived_response = get_long_lived_request.json()
                        long_lived_page_token = long_lived_response['access_token']
                return long_lived_page_token,page_id
            except IndexError:
                long_lived_page_token = access_token
                return long_lived_page_token,page_id
        else:
            return '',0
    except Exception :
        return redirect('https://flowkar.com/close/')
    

def get_facebook_user_info(access_token):
    url = "https://graph.facebook.com/v18.0/me"
    params = {
        "fields": "id,name,picture.type(large),link,username",
        "access_token": access_token
    }

    response = requests.get(url, params=params)
    print("response",response)

    if response.status_code == 200:
        data = response.json()

        # Extract required fields
        name = data.get('name')
        profile_url = data.get('link')
        profile_image = data.get('picture', {}).get('data', {}).get('url')

        return {
            "name": name,
            "profile_url": profile_url,
            "profile_image": profile_image
        }

    else:
        return {"error": response.json()}