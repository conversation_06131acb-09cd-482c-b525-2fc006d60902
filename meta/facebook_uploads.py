import requests
import json 

def upload_facebook_images(page_id, access_token, urls,caption=None):
    page_id = page_id
    access_token = access_token
    image_urls = urls

    photo_ids = []

    for url in image_urls:
        response = requests.post(
            f"https://graph.facebook.com/v21.0/{page_id}/photos",
            params={
                "access_token": access_token,
                "url": url,
                "published": "false" 
            }
        )
        result = response.json()
        if "id" in result:
            photo_ids.append(result["id"]) 
        else:
            print("Error uploading image:", result)

    attached_media = [{"media_fbid": photo_id} for photo_id in photo_ids]


    post_url = f"https://graph.facebook.com/v21.0/{page_id}/feed"
    
    response = requests.post(
        post_url,
        params={
            "access_token": access_token,
            "message": caption
        },
        json={
            "attached_media": attached_media
        }
    )

    if response.status_code == 200:
        post_result = response.json()
        return True,post_result['id']
    else:
        return False,0
    
def upload_facebook_videos(page_id, access_token, video_url,caption=None):
    page_id = page_id
    access_token = access_token
    response = requests.post(
            f"https://graph.facebook.com/v21.0/{page_id}/videos",
            params={
                "access_token": access_token,
                "file_url": video_url,
                'title':caption,
                "published": "True"
            }
        )
    result = response.json()
    if response.status_code == 200:
        return True, result['id']
    else:
        return False, 0