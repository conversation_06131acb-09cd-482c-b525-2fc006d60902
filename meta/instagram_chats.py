import json
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

# GET MESSAGES 
def get_conversations(access_token):
    url = f"https://graph.instagram.com/v22.0/me/conversations?fields=id,participants,messages.limit(1){{message,created_time}}&access_token={access_token}"
    response = requests.get(url)
    data = response.json()
    conversations = []
    if 'data' in data:
        for convo in data['data']:
            conversations.append({
                'conversation_id': convo['id'],
                'participants': convo.get('participants', {}).get('data', []),
                'messages': convo.get('messages', {}).get('data', [])
            })
    
    return conversations


def get_messages_in_conversation(access_token, conversation_id):
    url = f"https://graph.instagram.com/v22.0/{conversation_id}?fields=messages&access_token={access_token}"
    response = requests.get(url)
    data = response.json()
    
    messages = []
    if 'messages' in data and 'data' in data['messages']:
        for msg in data['messages']['data']:
            messages.append(msg['id'])
    
    return messages


def get_message_details(access_token, message_id):
    url = f"https://graph.instagram.com/v22.0/{message_id}?fields=id,created_time,from,to,message,attachments{{id,name,mime_type,url,image_data,video_data}}&access_token={access_token}"
    response = requests.get(url)
    return response.json()


def get_full_conversation_list(access_token,convo_id):
    convo = {
        "messages":[]
    }
        
    message_ids = get_messages_in_conversation(access_token, convo_id)
    if message_ids:  
        for msg_id in message_ids:
                convo['messages'].append(get_message_details(access_token, msg_id))
    
        return True,convo['messages']
    else:
        return False,convo['messages']


def get_conversation_list(access_token):
    conversation_list = get_conversations(access_token)
    
    id_count = {}
    for item in conversation_list:
        for p in item["participants"]:
            id_count[p["id"]] = id_count.get(p["id"], 0) + 1

    common_ids = {id for id, count in id_count.items() if count > 1}
    max_id = max(id_count, key=id_count.get)

    filtered_data = []
    for item in conversation_list:
        item["participants"] = [p for p in item["participants"] if p["id"] not in common_ids]
        if item["participants"]:
            filtered_data.append(item)
    return max_id,filtered_data


# Example Usage:
# access_token = "IGQWRNcno5OWFJM1ZARd0RLclFEN2ljZA0pTTHd5b1BNbU5xcG5kNHRjOE56TmVPUWtGaWhLNEMwVTdhYUJMQjFIWGRJbTNCQ3l6VXE5SElWU3g5Mm9Pa2NqMVNOTGM2LTV2NnNLdm90WTE3UQZDZD"
# all_conversations = get_conversation_list(access_token)

# # Print formatted output
# import json
# print(json.dumps(all_conversations, indent=2))



# print('convo',get_message_list_details('IGQWRNcno5OWFJM1ZARd0RLclFEN2ljZA0pTTHd5b1BNbU5xcG5kNHRjOE56TmVPUWtGaWhLNEMwVTdhYUJMQjFIWGRJbTNCQ3l6VXE5SElWU3g5Mm9Pa2NqMVNOTGM2LTV2NnNLdm90WTE3UQZDZD','aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDY2NDQ5MzMwOTM2OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI3NjA2MTE2ODE1MzMwMTk1NDozMjEyOTc1NDY5ODM2MDkyNjc1NzQ2MjcwMzk3OTEwMjIwOAZDZD'))

# SEND MESSAGE 
def send_instagram_message(ig_id, access_token, to_user_id, message_text):
    url = f"https://graph.instagram.com/v22.0/{ig_id}/messages"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    data = {
        "recipient": {"id": to_user_id},
        "message": {"text": message_text}
    }

    response = requests.post(url, json=data, headers=headers)
    if response.status_code == 200 or response.status_code == 201:
        return True,response.json()
    else:
        return False,response.json()
    
def send_instagram_images_message(instagram_id, to_user_id, image_url,media_type ,access_token):
    url = f"https://graph.instagram.com/v22.0/{instagram_id}/messages"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "recipient": {"id": to_user_id},
        "message": {
            "attachment": {
                "type": media_type,
                "payload": {"url": image_url}
            }
        }
    }
    
    response = requests.post(url, headers=headers, data=json.dumps(payload))

    if response.status_code == 200 or response.status_code == 201:
        return True,response.json()
    else:
        return False,response.json()