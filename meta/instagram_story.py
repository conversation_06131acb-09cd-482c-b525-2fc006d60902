import requests
import time

def upload_instagram_stories(ig_user_id, access_token, media_urls):

    results = []
    api_version = "v18.0"  
    
    for media_url in media_urls:
        print(f"\nProcessing media: {media_url}")
        endpoint = f"https://graph.instagram.com/{api_version}/{ig_user_id}/media"
        
        payload = {
            "media_type": "STORIES",
            "video_url" if media_url.endswith(('.mp4', '.mkv', '.mov', '.avi', '.flv', '.wmv', '.webm','.m4v', '.mpg', '.mpeg', '.3gp', '.3g2', '.mts', '.m2ts','.ts', '.ogv', '.rm', '.rmvb')) else "image_url": media_url,
            "access_token": access_token
        }

        # Step 1: Create the story media container
        response = requests.post(endpoint, data=payload)
        if response.status_code != 200:
            results.append({"status": False, "message": f"Failed to create media container for {media_url}", "response": response.json()})
            continue
        
        container_id = response.json().get("id")
        
        if not container_id:
            results.append({"status": False, "message": f"No container ID returned for {media_url}"})
            continue

        print(f"Media container created successfully. Container ID: {container_id}")

        # Step 2: Publish the story with retry mechanism
        publish_endpoint = f"https://graph.instagram.com/{api_version}/{ig_user_id}/media_publish"
        publish_payload = {
            "creation_id": container_id,
            "access_token": access_token
        }

        max_retries = 5  
        initial_delay = 5  
        success = False

        for attempt in range(max_retries):
            if attempt > 0:
                current_delay = initial_delay * (2 ** (attempt - 1))
                print(f"Waiting {current_delay} seconds before retry {attempt + 1}...")
                time.sleep(current_delay)

            publish_response = requests.post(publish_endpoint, data=publish_payload)
            
            if publish_response.status_code == 200:
                success = True
                print(f"Successfully published story for {media_url}")
                break
            else:
                error_data = publish_response.json().get('error', {})
                error_message = error_data.get('message', 'Unknown error')
                
                if "Media ID is not available" in str(error_message):
                    print(f"Media still processing, attempt {attempt + 1} of {max_retries}")
                    continue
                else:
                    print(f"Unexpected error: {error_message}")
                    break

        if not success:
            results.append({"status": False,"message": f"Failed to publish story for {media_url} after {max_retries} attempts"})
            continue

        results.append({
            "status": True, 
            "message": f"Story uploaded successfully for {media_url}",
            "response": publish_response.json()
        })
    
    if results[0]["status"] == True:
        return True, results
    else:
        return False, results


# ig_user_id = "9315634598506176"
# access_token = "IGQWRPSzVSMDU4QU9OZAjUxajY3YzJzM3lzMWtCYzZAKNk1pN0plNzFMZAk5CSGt1WExyb2JfSFdLWWZAEbXNPVkx5WkdXdFlSelNiMjVlQks2NklpalZAzM3VaaVgzbzB6TWNRQzY5M1J2Wml3ZAwZDZD"

# media_urls = [
#     "https://api.flowkar.com/media/post_files/123.jpeg",
#     "https://api.flowkar.com/media/post_files/insta_assets_crop_A8F5294C-C9B0-4EF5-93C5-5371EF176454-38144-00001249647397D8.jpeg",
# ]

# results = upload_instagram_stories(ig_user_id, access_token, media_urls)

# print(results)