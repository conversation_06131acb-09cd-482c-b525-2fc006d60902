import requests

def get_instagram_likes_and_comments(post_id,access_token):
    url = f"https://graph.instagram.com/{post_id}"
    params = {
        'fields':'like_count,comments_count',
        'access_token':access_token,
    }
    response = requests.get(url,params=params)
    data = response.json()
    if response.status_code == 200:
        likes = data['like_count']
        comments = data['comments_count']
        return likes,comments
    else:
        return 0,0
    
def get_instagram_likes_and_comments_url(post_id,access_token):
    url = f"https://graph.instagram.com/{post_id}"
    params = {
        'fields':'like_count,comments_count,media_url',
        'access_token':access_token,
    }
    response = requests.get(url,params=params)
    data = response.json()
    if response.status_code == 200:
        likes = data['like_count']
        comments = data['comments_count']
        url = data['media_url']
        return likes,comments,url
    else:
        return 0,0,''
    

def get_post_share_url(token,post_id):
    url = f"https://graph.instagram.com/v21.0/{post_id}?fields=permalink"

    headers = {
        'Authorization': f'Bearer {token}'
    }

    response = requests.request("GET", url, headers=headers)
    if response.status_code == 200:
        return True,response.json()['permalink']
    else:
        return False,''