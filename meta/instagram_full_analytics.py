from datetime import datetime, timedelta
import requests

def get_instagram_data(instagram_account_id, access_token, start_date, end_date):
    try:
        media_endpoint = f"https://graph.instagram.com/v17.0/{instagram_account_id}/media"
        
        media_params = {
            "fields": "id,timestamp",
            "access_token": access_token,
        }
        
        media_response = requests.get(media_endpoint, params=media_params)

        if media_response.status_code != 200:
            return {"status_code": media_response.status_code, "error": media_response.json()}
        
        media_data = media_response.json().get("data", [])

        total_posts_count = len(media_data)

        date_range = [(start_date + timedelta(days=i)).strftime("%Y-%m-%d") 
                    for i in range((end_date - start_date).days + 1)]


        metrics = ["impressions", "reach", "likes", "comments", "shares", "profile_visits", "total_interactions","profile_activity", "saved"]
        
        insights_data_dict = {metric: {date: 0 for date in date_range} for metric in metrics}
        post_counts = {date: 0 for date in date_range}
        followers_insights = {date: 0 for date in date_range}
        following_counts = {date: 0 for date in date_range}

        for media in media_data:
            post_id = media.get("id")
            post_date = media.get("timestamp", "")[:10]

            if post_date in post_counts:
                post_counts[post_date] += 1

                insights_endpoint = f"https://graph.instagram.com/{post_id}/insights"
                insights_params = {
                    "metric": ",".join(metrics),
                    "access_token": access_token
                }
                
                insights_response = requests.get(insights_endpoint, params=insights_params)
                if insights_response.status_code == 200:
                    insights_list = insights_response.json().get("data", [])

                    for insight in insights_list:
                        metric_name = insight.get("name")
                        value = insight.get("values", [{}])[0].get("value", 0)
                        
                        if metric_name in insights_data_dict:
                            insights_data_dict[metric_name][post_date] += value

        profile_endpoint = f"https://graph.instagram.com/v17.0/{instagram_account_id}"
        profile_params = {
            "fields": "followers_count,follows_count,media_count",
            "access_token": access_token
        }
        
        profile_response = requests.get(profile_endpoint, params=profile_params)
        followers_count = 0
        following_count = 0
        total_posts = total_posts_count

        if profile_response.status_code == 200:
            profile_data = profile_response.json()
            followers_count = profile_data.get("followers_count", 0)
            following_count = profile_data.get("follows_count", 0)
            total_posts = profile_data.get("media_count", total_posts_count) 


        followers_insights_endpoint = f"https://graph.instagram.com/v17.0/{instagram_account_id}/insights"
        followers_insights_params = {
            "metric": "follower_count",
            "period": "day",
            "since": int(start_date.timestamp()),
            "until": int(end_date.timestamp()),
            "access_token": access_token
        }
        
        followers_insights_response = requests.get(followers_insights_endpoint, params=followers_insights_params)
        if followers_insights_response.status_code == 200:
            insights_data = followers_insights_response.json().get("data", [])
            if insights_data:
                for entry in insights_data[0].get("values", []):
                    followers_insights[entry["end_time"][:10]] = entry["value"]

        for date in date_range:
            following_counts[date] = following_count

        response_data = {
            "following_count": following_count,
            "followers_count": followers_count,
            "total_posts": total_posts,
            "post_counts": post_counts,
            "total_posts_in_date_range": sum(post_counts.values()),
            "followers_insights": followers_insights,
            "total_followers_graph": sum(followers_insights.values())
        }

        for metric in metrics:
            response_data[f"{metric}_counts"] = insights_data_dict[metric]
            response_data[f"total_{metric}_in_date_range"] = sum(insights_data_dict[metric].values())

        return True,response_data
    except Exception as e :
        return False,{}



def get_instagram_insights(instagram_account_id, access_token,parameter,metrics="follower_demographics",period="lifetime", metric_type="total_value"):

        endpoint = f"https://graph.instagram.com/v22.0/{instagram_account_id}/insights"

        params = {
            "metric": metrics,
            "period": period,
            "metric_type": metric_type,
            "access_token": access_token,
            "breakdown":parameter
        }

        response = requests.get(endpoint, params=params)
        data = {}
        try:
            if response.status_code == 200:
                return True,response.json().get('data')[0].get('total_value').get('breakdowns')[0]['results']
            else:
                return False,[]
        except (TypeError,IndexError,KeyError):
            return False,[]




# instagram_account_id = "****************"
# access_token = "IGQWROREtqZA2h4UlNJOVp2dGZAVS0ZA0QlBPcjZALZAWxwVndwOEVZAeXlTUk5MWGdVQVJYc1ozZAk8yemdBRlJTTWNIQVdRZAkhJQkhyRkpHUVFzemZA5YXpnUHZAPOTFGaFFKc0ZARUXpQZAUxaQnFYdwZDZD"
# metrics = "follower_demographics"

# insights_data = get_instagram_insights(instagram_account_id, access_token, metrics)
# print(insights_data)


# # Inputs
# instagram_account_id = "****************"
# access_token = "IGQWROREtqZA2h4UlNJOVp2dGZAVS0ZA0QlBPcjZALZAWxwVndwOEVZAeXlTUk5MWGdVQVJYc1ozZAk8yemdBRlJTTWNIQVdRZAkhJQkhyRkpHUVFzemZA5YXpnUHZAPOTFGaFFKc0ZARUXpQZAUxaQnFYdwZDZD"
# start_date = datetime(2025, 3, 1)  
# end_date = datetime(2025, 3, 26)    

# post_data = get_instagram_data(instagram_account_id, access_token, start_date, end_date)
# print(post_data)