import requests
import os

def upload_multiple_facebook_stories(page_id, access_token, media_paths):
    results = []

    for media_path in media_paths:
        try:
            # Step 1: Upload photo (unpublished)
            upload_url = f"https://graph.facebook.com/v23.0/{page_id}/photos"
            upload_payload = {
                "published": "false",
                "access_token": access_token
            }

            upload_files = {
                "source": open(media_path, "rb")
            }

            upload_response = requests.post(upload_url, data=upload_payload, files=upload_files)
            upload_files["source"].close()

            if upload_response.status_code != 200:
                results.append({
                    "file": media_path,
                    "status": False,
                    "message": "Upload failed",
                    "response": upload_response.json()
                })
                continue

            media_id = upload_response.json().get("id")

            # Step 2: Publish as a story
            story_url = f"https://graph.facebook.com/v23.0/{page_id}/photo_stories"
            story_payload = {
                "photo_id": media_id,
                "access_token": access_token
            }

            story_response = requests.post(story_url, data=story_payload)

            if story_response.status_code == 200:
                results.append({
                    "file": media_path,
                    "status": True,
                    "message": "Story uploaded successfully",
                    "response": story_response.json()
                })
            else:
                results.append({
                    "file": media_path,
                    "status": False,
                    "message": "Story publish failed",
                    "response": story_response.json()
                })

        except Exception as e:
            results.append({
                "file": media_path,
                "status": False,
                "message": f"Exception occurred: {e}"
            })

    # Check if all uploads were successful
    all_successful = all(result["status"] for result in results)
    
    if all_successful:
        return True, results
    else:
        return False, results

# page_id = "300731683602563"
# access_token = "EAAVeFVzn8eUBO0OGVXaLPiOr2ABC6niK2NU5ZBtQKzwY1VVlpGlJ0iMDgxgNf4BdgQF5y4JUxTP4UO1iBdUv6MZCnMjshhZCKaHgKoySzWKXwdgEkF5ZCzswGBUKYaoLPi54K2ZA5JNWGZAyZCoRkCCZBhkv45TXdtRmmyagZBGjowhfdBCIewZBbcXTxka6IHUyNzu5CaJBw2uQnnOC2WECLM"
# media_files = [
#     "1.jpeg"
# ]

# response = upload_multiple_facebook_stories(page_id, access_token, media_files)

# for r in response:
#     print(r)