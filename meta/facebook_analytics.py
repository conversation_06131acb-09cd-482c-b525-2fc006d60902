import requests

def get_facebook_likes_and_comments(post_id,access_token):
    url = f"https://graph.facebook.com/v21.0/{post_id}"
    params = {
        'fields':'likes.summary(true),comments.summary(true)',
        'access_token':access_token,
    }
    response = requests.get(url,params=params)
    data = response.json()
    if response.status_code == 200:
        likes = data['likes']['summary']['total_count']
        comments = data['comments']['summary']['total_count']
        return likes,comments
    else:
        return 0,0