import time
import requests

def upload_images_to_instagram(user_id, access_token, image_urls,caption=None):
    if len(image_urls) <= 1:
        url = f'https://graph.instagram.com/v21.0/{user_id}/media'
        data = {
            'image_url': image_urls[0],
            'access_token': access_token,
            'caption':caption,
        }
        
        response = requests.post(url, data=data)
        if response.status_code == 200:
            response_data = response.json()
            publish_single_url = f'https://graph.instagram.com/v21.0/{user_id}/media_publish'
            single_publish_body = {
                'creation_id':response_data['id'],
                'access_token':access_token
            }
            post_single_publish = requests.post(publish_single_url,data=single_publish_body)
            post_single_response = post_single_publish.json()
            if post_single_publish.status_code == 200:
                return True , post_single_response['id']
            else:
                return False, 0
        else:
            print("Error uploading image:", response.json())
            return False, 0
        
    media_ids = []

    # Upload each image
    for image_url in image_urls:
        # Step 1: Upload the image
        url = f'https://graph.instagram.com/v21.0/{user_id}/media'
        data = {
            'image_url': image_url,
            'access_token': access_token,
            'caption':caption,
            'media_type': 'IMAGE'
        }
        
        response = requests.post(url, data=data)
        if response.status_code == 200:
            response_data = response.json()
            media_ids.append(response_data['id'])  # Collect media IDs for the carousel
        else:
            print("Error uploading image:", response.json())
            return False, 0

    # Step 2: Create a carousel container
    carousel_url = f'https://graph.instagram.com/v21.0/{user_id}/media'
    carousel_data = {
        'media_type': 'CAROUSEL',
        'children': ','.join(media_ids),  # List of uploaded image media IDs
        'access_token': access_token,
        'caption':caption
    }
    
    carousel_response = requests.post(carousel_url, data=carousel_data)
    
    if carousel_response.status_code == 200:
        carousel_response_data = carousel_response.json()
        creation_id = carousel_response_data['id']
    else:
        print("Error creating carousel:", carousel_response.json())
        return False, 0

    # Step 3: Publish the carousel
    publish_url = f'https://graph.instagram.com/v21.0/{user_id}/media_publish'
    publish_data = {
        'creation_id': creation_id,
        'access_token': access_token
    }

    publish_response = requests.post(publish_url, data=publish_data)

    if publish_response.status_code == 200:
        publish_response_data = publish_response.json()
        return True, publish_response_data['id']
    else:
        print("Error publishing post:", publish_response.json())
        return False, 0
    

def upload_and_publish_instagram_video(user_id, access_token, video_url, caption=None):
    # Step 1: Upload the video
    url = f'https://graph.instagram.com/v21.0/{user_id}/media'
    data = {
        'video_url': video_url,
        'caption': caption,
        'access_token': access_token,
        'media_type': 'REELS'  # For video posts (can also be 'VIDEO' if it's a normal video post)
    }

    upload_request = requests.post(url, data=data)
    
    if upload_request.status_code == 200:
        upload_response = upload_request.json()
        creation_id = upload_response['id']  # Get the creation ID for the uploaded video
        
        # Wait for the media to be processed by Instagram
        print(f"Waiting for media {creation_id} to be processed...")

        # Step 2: Check the media status and wait until it's ready
        media_status_url = f'https://graph.instagram.com/v21.0/{creation_id}'
        status_data = {
            'access_token': access_token
        }

        # Retry until media is ready or a timeout occurs
        retries = 10  # Set a retry limit
        for _ in range(retries):
            status_request = requests.get(media_status_url, params=status_data)
            if status_request.status_code == 200:
                status_response = status_request.json()
                if status_response.get('status') == 'FINISHED':  # Check if the media is ready
                    print(f"Media {creation_id} is ready to be published.")
                    break
            print(f"Waiting for media {creation_id} to be processed... (Retry {_ + 1} of {retries})")
            time.sleep(6)  # Wait 15 seconds before checking again
        else:
            # If retries exhausted, fail the process
            print("Failed to upload media, retry limit reached.")
            return False, 0
        
        # Step 3: Publish the video
        publish_url = f'https://graph.instagram.com/v21.0/{user_id}/media_publish'
        publish_data = {
            'creation_id': creation_id,
            'access_token': access_token
        }

        publish_request = requests.post(publish_url, data=publish_data)
        
        # Check if the response is valid and not empty
        if publish_request.status_code == 200:
            publish_response = publish_request.json()
            print(f"Video published successfully with ID: {publish_response['id']}")
            return True, publish_response['id']  # Return the ID of the published video
        else:
            print(f"Error publishing post: {publish_request.status_code} - {publish_request.text}")
            return False, 0
    else:
        # If the upload failed, log the error and response content
        print(f"Error uploading video: {upload_request.status_code} - {upload_request.text}")
        return False, 0


def create_carousel_post(user_id, access_token, media_urls, caption=""):
    graph_api_url = f"https://graph.instagram.com/v22.0/{user_id}"
    container_ids = []
    small_file_check = ( '.mp4', '.mkv', '.mov', '.avi', '.flv', '.wmv', '.webm','.m4v', '.mpg', '.mpeg', '.3gp', '.3g2', '.mts', '.m2ts','.ts', '.ogv', '.rm', '.rmvb')
    upper_file_check  =( '.MP4', '.MKV', '.MOV', '.AVI', '.FLV', '.WMV', '.WEBM','.M4V', '.MPG', '.MPEG', '.3GO', '.3G2', '.MTS', '.M2TS','.TS', '.OGV', '.RM', '.RMVB')
    for url in media_urls:
        payload = {
            "is_carousel_item": True,
            "access_token": access_token
        }
        
        if url.endswith(small_file_check) or url.endswith(upper_file_check):
            payload["media_type"] = "VIDEO"
            payload["video_url"] = url
        else:
            payload["image_url"] = url

        response = requests.post(f"{graph_api_url}/media", json=payload)
        if response.status_code == 200:
            container_id = response.json().get("id")
            container_ids.append(container_id)
        else:
            return None
        
        print(payload)
        print(response.json())
        
    
    time.sleep(30)

    carousel_payload = {
        "media_type": "CAROUSEL",
        "children": ",".join(container_ids),
        "caption": caption,
        "access_token": access_token
    }

    response = requests.post(f"{graph_api_url}/media", json=carousel_payload)

    print(response.json())
    print(response.status_code)

    if response.status_code == 200:
        carousel_id = response.json().get("id")
    else:
        return None
    


    time.sleep(10)

    publish_payload = {
        "creation_id": carousel_id,
        "access_token": access_token
    }
    time.sleep(10)
    response = requests.post(f"{graph_api_url}/media_publish", json=publish_payload)
    print(response.json())
    if response.status_code == 200:
        media_id = response.json().get("id")
        return media_id
    else:
        return None
    


# # Example usage
# user_id = "9074356289360452"
# access_token = "IGQWRNZA09EUHhXX19rSklrNlAxdVc4ZAXJvWjFkM0VMdm1EazVDVXNUOTdnc2JPbE0zc1hmWHJMM3BDamF4RmRaZAFQxYlpDN3BNOFpxaXVvUV9sczdabi1WbjNUekhjZAWdVSFFXTHhMajJYQQZDZD"
# media_urls = [
#     "https://api.flowkar.com/media/post_files/insta_assets_crop_C4040967-F945-444A-81A6-B4CE1032F321-37020-00000B4C9D535090.jpeg",
#     "https://api.flowkar.com/media/post_files/compressed_video_20250407_110920.mp4",
# ]

# "https://api.flowkar.com/media/post_files/compressed_03B55AFF-22A9-45BD-A4CD-32AE03EE3888_cncteZB.mp4"

# media_id = create_carousel_post(user_id, access_token, media_urls,caption="")
# print("Returned Media ID:", media_id)



# user_id = 9395201533842828
# access_token = 'IGQWRQUzJtLVJFU0RJcjk1eFNhXzBlRl80ZAjEwemZAHdmp0S2IxWEZAvUXJqdHZAzN0lFb3F6dUY0YW55TUFMNzFKeWNCQ3JhLUhyZAVB2NUFBRXhuZA0YyaERiZAHRkZAndBUnZAlc1lBTG40N0lpdwZDZD'
# video_url = 'https://staging.yooii.com/media/post_files/compressed_VID-20241024-WA0001.mp4'
# caption = ' Test Multiple Images To Instagram'
# print(upload_and_publish_instagram_video(user_id,access_token,video_url,caption))