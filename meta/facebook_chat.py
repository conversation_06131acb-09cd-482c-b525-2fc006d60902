import requests
from django.http import JsonResponse

#@PAGE_ACCESS_TOKEN = "EAAVeFVzn8eUBOwdz1w0GzLiX1qf2AP0owgaJQE63ZCVLVMdY1sVKRBByU7Ms6zhYaNp1e0rgaeTVd8zlYH5CePxsDx7wtYhFN7ZCfKhfY25o7mXEDZCoPZBm2AYRINmuFULehwnyMEgba5fMrJ9ZA4YNuuaPHZAkUXRZACZBQXZAzlD9Y6pJR0WXhOfYvFbgG9HsOAyGVBBP9ob0f0IMALwUwnD6s"
#PAGE_ID = "300731683602563"

def get_facebook_messages(page_access_token,convo_id):
    all_messages = []

    message_url = f"https://graph.facebook.com/v19.0/{convo_id}"
    message_params = {
                "fields": "messages{message,created_time,from,to,attachments{image_data{url}, video_data{url,preview_url}}}",
                "access_token": page_access_token
        }

    message_response = requests.get(message_url, params=message_params)
    message_data = message_response.json()
    if message_response.status_code == 200 or message_response.status_code == 201:
        messages = message_data.get("messages", {}).get("data", [])
        formatted_messages = []
        for msg in messages:
            image_url = None
            video_url = None
            video_type = None
                    
            if "attachments" in msg:
                for attachment in msg["attachments"].get("data", []):
                    if "image_data" in attachment:
                                image_url = attachment["image_data"]["url"]
                    if "video_data" in attachment:
                            video_url = attachment["video_data"]["url"].split("&dl=1")[0]
                            if ".mp4" in video_url.lower():
                                    video_type = "video/mp4"
                            elif ".webm" in video_url.lower():
                                    video_type = "video/webm"
                            elif ".ogg" in video_url.lower():
                                    video_type = "video/ogg"
                            else:
                                    video_type = "video/mp4" 

            formatted_msg = {
                            "id": msg["id"],
                            "message": msg.get("message", ""),
                            "image": image_url,
                            "video": video_url,
                            "video_type": video_type,
                            "created_time": msg["created_time"],
                            "from": msg["from"],
                            "to": msg["to"]
                        }

            all_messages.append(formatted_msg)

        return True,all_messages
    else:
         return False,None





def get_facebook_conversations(page_access_token,page_id):
    url = f"https://graph.facebook.com/v19.0/{page_id}/conversations"
    params = {
        "platform": "messenger",
        "fields": "participants,messages{message,created_time}",
        "access_token": page_access_token
    }

    response = requests.get(url, params=params)
    data = response.json()
    if "data" in data:
        conversations = data["data"]
        all_conversations = []

        for convo in conversations:
            convo_id = convo["id"]
            participants = convo.get("participants", {}).get("data", [])
            messages = convo.get("messages", {}).get("data", [])
            participant_details = [
                {"name": p["name"], "psid": p["id"]} for p in participants
            ]

            all_conversations.append({
                "conversation_id": convo_id,
                "participants": participant_details,
                "messages": messages
            })

        return all_conversations
    
# SEND MESSAGE

def send_facebook_message(page_id, page_access_token, to_conversation_id, message_text):
    url = f"https://graph.facebook.com/v22.0/{page_id}/messages"
    headers = {"Content-Type": "application/json"}
    payload = {
        "recipient": {"id": to_conversation_id},
        "messaging_type": "RESPONSE",
        "message": {"text": message_text}
    }

    response = requests.post(url, headers=headers, json=payload, params={"access_token": page_access_token})

    if response.status_code == 200 or response.status_code == 201:
        return True,response.json()
    else:
        return False,response.json()
    

def send_facebook_media_message(page_access_token, to_conversation_id, media_url, media_type="image"):
    if media_type not in ["image", "video"]:
        return {"error": "Invalid media type. Use 'image' or 'video'."}

    url = "https://graph.facebook.com/v22.0/me/messages"
    headers = {"Content-Type": "application/json"}
    payload = {
        "recipient": {"id": to_conversation_id},
        "message": {
            "attachment": {
                "type": media_type,
                "payload": {
                    "url": media_url,
                    "is_reusable": True
                }
            }
        }
    }

    response = requests.post(url, headers=headers, json=payload, params={"access_token": page_access_token})
    
    if response.status_code == 200 or response.status_code == 201:
        return True,response.json()
    else:
        return False,response.json()