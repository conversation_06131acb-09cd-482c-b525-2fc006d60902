import requests
import time
from datetime import datetime, timedelta


def get_threads_analytics_one(access_token, threads_id):
    url = f"https://graph.threads.net/v1.0/{threads_id}/threads_insights"
    params = {
        "metric": "follower_demographics",
        "access_token": access_token,
        "period": "lifetime",
        "breakdown": "country"
    }
    
    insights = {}

    try:
        response = requests.get(url, params=params)
        print(f"Response (country) --->", response.status_code)
        data = response.json()
        formatted_data = {}

        if "data" in data and data["data"]:
            for item in data["data"]:
                if "total_value" in item and "breakdowns" in item["total_value"]:
                    for breakdown_item in item["total_value"]["breakdowns"]:
                        for result in breakdown_item["results"]:
                            key = result["dimension_values"][0]
                            value = result["value"]
                            formatted_data[key] = value

    #     insights["country"] = formatted_data if formatted_data else {"default": 0}
    
    # except requests.exceptions.RequestException as e:
    #     insights["country"] = {"error": str(e)}
    
    # return True, insights

        if formatted_data:
            insights["country"] = formatted_data
            return True, insights
        else:
            insights["country"] = {"default": 0}
            return False, insights

    except requests.exceptions.RequestException as e:
        insights["country"] = {"error": str(e)}
        return False, insights
    


def get_threads_analytics_two(access_token, threads_id):
    url = f"https://graph.threads.net/v1.0/{threads_id}/threads_insights"
    params = {
        "metric": "follower_demographics",
        "access_token": access_token,
        "period": "lifetime",
        "breakdown": "city"
    }
    insights = {}

    try:
        response = requests.get(url, params=params)
        print(f"Response (city) --->", response.status_code)
        data = response.json()
        formatted_data = {}

        if "data" in data and data["data"]:
            for item in data["data"]:
                if "total_value" in item and "breakdowns" in item["total_value"]:
                    for breakdown_item in item["total_value"]["breakdowns"]:
                        for result in breakdown_item["results"]:
                            key = result["dimension_values"][0]
                            value = result["value"]
                            formatted_data[key] = value

    #     insights["city"] = formatted_data if formatted_data else {"default": 0}
    
    # except requests.exceptions.RequestException as e:
    #     insights["city"] = {"error": str(e)}
    
    # return True, insights

        if formatted_data:
            insights["city"] = formatted_data
            return True, insights
        else:
            insights["city"] = {"default": 0}
            return False, insights

    except requests.exceptions.RequestException as e:
        insights["city"] = {"error": str(e)}
        return False, insights
    


def get_threads_analytics_three(access_token, threads_id):
    url = f"https://graph.threads.net/v1.0/{threads_id}/threads_insights"
    params = {
        "metric": "follower_demographics",
        "access_token": access_token,
        "period": "lifetime",
        "breakdown": "age"
    }
    insights = {}

    try:
        response = requests.get(url, params=params)
        print(f"Response (age) --->", response.status_code)
        data = response.json()
        formatted_data = {}

        if "data" in data and data["data"]:
            for item in data["data"]:
                if "total_value" in item and "breakdowns" in item["total_value"]:
                    for breakdown_item in item["total_value"]["breakdowns"]:
                        for result in breakdown_item["results"]:
                            key = result["dimension_values"][0]
                            value = result["value"]
                            formatted_data[key] = value

    #     insights["age"] = formatted_data if formatted_data else {"default": 0}
    
    # except requests.exceptions.RequestException as e:
    #     insights["age"] = {"error": str(e)}
    
    # return True, insights


        if formatted_data:
            insights["age"] = formatted_data
            return True, insights
        else:
            insights["age"] = {"default": 0}
            return False, insights

    except requests.exceptions.RequestException as e:
        insights["age"] = {"error": str(e)}
        return False, insights


def get_threads_analytics_four(access_token, threads_id):
    url = f"https://graph.threads.net/v1.0/{threads_id}/threads_insights"
    params = {
        "metric": "follower_demographics",
        "access_token": access_token,
        "period": "lifetime",
        "breakdown": "gender"
    }
    insights = {}

    try:
        response = requests.get(url, params=params)
        print(f"Response (gender) --->", response.status_code)
        data = response.json()
        formatted_data = {}

        if "data" in data and data["data"]:
            for item in data["data"]:
                if "total_value" in item and "breakdowns" in item["total_value"]:
                    for breakdown_item in item["total_value"]["breakdowns"]:
                        for result in breakdown_item["results"]:
                            key = result["dimension_values"][0]
                            value = result["value"]
                            formatted_data[key] = value

    #     insights["gender"] = formatted_data if formatted_data else {"default": 0}
    
    # except requests.exceptions.RequestException as e:
    #     insights["gender"] = {"error": str(e)}
    
    # return True, insights

        if formatted_data:
            insights["gender"] = formatted_data
            return True, insights
        else:
            insights["gender"] = {"default": 0}
            return False, insights

    except requests.exceptions.RequestException as e:
        insights["gender"] = {"error": str(e)}
        return False, insights






def date_to_unix(timestamp):
    return int(time.mktime(datetime.strptime(timestamp, "%Y-%m-%d").timetuple()))

def threads_analytics_five(access_token, threads_id,start_date, end_date):
    new_start_date = (datetime.strptime(start_date, "%Y-%m-%d") + timedelta(days=1)).strftime("%Y-%m-%d")
    new_end_date = (datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)).strftime("%Y-%m-%d")

    url = f"https://graph.threads.net/v1.0/{threads_id}/threads_insights"
    
    params = {
        "metric": 'views',
        "since": date_to_unix(new_start_date),
        "until": date_to_unix(new_end_date),
        "access_token": access_token
    }
    try:
        response = requests.get(url, params=params)
        data = response.json()
        
        views_data = [
            {"date": item['end_time'][:10], "value": item['value']}
            for item in data.get('data', [])[0].get('values', [])
        ]
        
        total_views = sum(item['value'] for item in views_data)

        return True, {"views": views_data, "total_views": total_views}

    except Exception as e:
        return False, {}
    

    
# access_token = "THAAozCtE1fj9BYlUzOFB5dWZAVMlZApd3RKNDhrM3hBb18zNGJudVpjN1doNWF2X201SmZAQTUNYOW9GZAHdFT2hVclNtaVVab25UVzF4b2FJbHJCVldBSU9peVhVSDNoc05ONmRnQl8tZAFY4OV9ITXBjbmo2WnMtZAmpvcjFhMlNCU1NUZAwZDZD"
# threads_id = "9531550306931110"

# insights = get_threads_insights(access_token, threads_id)
# print(insights)