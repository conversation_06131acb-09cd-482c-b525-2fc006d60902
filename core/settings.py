import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent


SECRET_KEY = 'django-insecure-k1k6bt2kh!-#nh#nhw0&23s5_p&t$ha89g=)(h=&26#nfl!f+x'

DEBUG = True

ALLOWED_HOSTS = ['staging.yooii.com', '127.0.0.1', 'yooii.com','dev.yooii.com','localhost','staging.flowkar.com','api.flowkar.com','dev.flowkar.com','beta.flowkar.com']
# ALLOWED_HOSTS = ['staging.yooii.com', '127.0.0.1', 'yooii.com','dev.yooii.com','localhost','staging.khaoes.com','staging.flowkar.com','dev.flowkar.com']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework_simplejwt',
    'Authentication',
    'django.contrib.sites',
    'corsheaders',
    'Project'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',

    # Must be high in the list
    'corsheaders.middleware.CorsMiddleware',

    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',


    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]


ROOT_URLCONF = 'core.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'core.wsgi.application'


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'db_flowkar',
        'USER': 'flowkar',
        'PASSWORD': 'Flowkar@136',
        'HOST': '***************',
        'PORT': '5432',
        'CONN_MAX_AGE': 180,  # Keeps connections alive for 3 minutes
        'OPTIONS': {
            'connect_timeout': 10,  # Prevents long waits for dead connections
        }
    }
}

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_HEADERS = [
  "authorization",
  "content-type",
  "x-csrf-token",
  "subscription",
  "brand",
  "owner",
  "platform",
  "conversation",
  "user",
]

CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:5173",
    "http://localhost:5173",
    "http://127.0.0.1:5174",
    "http://localhost:5174",
    "http://staging.flowkar.com",
    "http://api.flowkar.com",
    "https://staging.flowkar.com",
    "https://staging.yooii.com",
    "https://dev.flowkar.com",
    "http://dev.flowkar.com",
    "http://beta.flowkar.com",
    "https://beta.flowkar.com",
    "http://app.flowkar.com",
    "https://app.flowkar.com",
    "https://flowkar.com",
    "http://flowkar.com",
    "https://api.flowkar.com",
]

CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_BEAT_SCHEDULE = {
    'task_every_5_seconds': {
        'task': 'core.tasks.my_scheduled_task',
        'schedule': 5.0,
    },
}

# SECURE_SSL_REDIRECT = True
# SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')


MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

ENCRYPTION_KEY = b"nNjpIl9Ax2LRtm-p6ryCRZ8lRsL0DtuY0f9JeAe2wG1="

RAZORPAY_WEBHOOK_SECRET = 'hwNbq@DtK39gi5N'

ONESIGNAL_APP_ID = '************************************'
ONESIGNAL_API_KEY = 'os_v2_app_524cccz7ofd2xozgap7zltafsj24befclsmem4e4sjtwbygsm6cgyffxibba3gr5gfeorwfxypqgi45od36o7whomenjfhiet6bf3zi'

# ONESIGNAL_APP_ID = '************************************'
# ONESIGNAL_API_KEY = '************************************************'

#SMTP

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'hmuiasknrmwlclzn'
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'
USE_TZ = True  

USE_I18N = True

USE_TZ = True

#tumblr 
CONSUMER_KEY = '23pJ5t0JKNSORTvsY2e9HyQF002wMpYlk01mce4V7C0yLkHFuh'
CONSUMER_SECRET = '1t5FfgSfdAHHNu4brSTlCC0ecw9NG9mqBI1lqaKZZSFGOQbyqm'

REQUEST_TOKEN_URL = 'https://www.tumblr.com/oauth/request_token'
req = 'https://www.tumblr.com/oauth/access_token'
AUTHORIZE_URL = 'https://www.tumblr.com/oauth/authorize'

STATIC_URL = 'static/'

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


#sentry
import sentry_sdk

# sentry_sdk.init(
#     dsn="https://<EMAIL>/4509093656657920",
#     # Add data like request headers and IP for users, if applicable;
#     # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
#     send_default_pii=True,
#     # We recommend adjusting this value in production
#     traces_sample_rate=1.0,
#     )



