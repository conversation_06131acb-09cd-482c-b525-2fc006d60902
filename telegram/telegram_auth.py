import asyncio
import os
from telethon import Telegram<PERSON>lient
from telethon.errors import SessionPasswordNeededError
from telethon.tl.functions.auth import SendCodeRequest, SignInRequest, CheckPasswordRequest
from telethon.tl.functions.users import GetUsersRequest
from telethon.tl.types import InputPhoneContact
import json
import os

# Telegram API credentials
API_ID = 20715292
API_HASH = 'f8cd7e4a00102f0fed3304a2b4851293'

class TelegramAuth:
    def __init__(self, session_name='telegram_session'):
        self.api_id = API_ID
        self.api_hash = API_HASH

        session_dir = os.path.join(os.path.dirname(__file__), 'session')
        os.makedirs(session_dir, exist_ok=True)
        self.session_name = os.path.join(session_dir, session_name)
        self.client = None
    
    async def create_client(self):
        self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
        return self.client
    
    async def send_code_request(self, phone_number):
        try:
            if not self.client:
                await self.create_client()
            
            await self.client.connect()
            
            result = await self.client.send_code_request(phone_number)
            
            return {
                'success': True,
                'phone_code_hash': result.phone_code_hash,
                'message': 'Code sent successfully'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to send code'
            }
    
    async def sign_in(self, phone_number, code, phone_code_hash, password=None):
        """Sign in with phone number and code"""
        try:
            if not self.client:
                await self.create_client()
            
            await self.client.connect()
            
            try:
                result = await self.client.sign_in(
                    phone=phone_number,
                    code=code,
                    phone_code_hash=phone_code_hash
                )
                
                me = await self.client.get_me()
                
                return {
                    'success': True,
                    'user_id': me.id,
                    'username': me.username,
                    'first_name': me.first_name,
                    'last_name': me.last_name,
                    'phone': me.phone,
                    'message': 'Signed in successfully'
                }
                
            except SessionPasswordNeededError:
                if password:
                    result = await self.client.sign_in(password=password)
                    me = await self.client.get_me()
                    
                    return {
                        'success': True,
                        'user_id': me.id,
                        'username': me.username,
                        'first_name': me.first_name,
                        'last_name': me.last_name,
                        'phone': me.phone,
                        'message': 'Signed in successfully with 2FA'
                    }
                else:
                    return {
                        'success': False,
                        'requires_password': True,
                        'message': 'Two-factor authentication required'
                    }
                    
        except Exception as e:
            return {'success': False,'error': str(e),'message': 'Failed to sign in'}
    
    async def disconnect(self):
        """Disconnect the client"""
        if self.client:
            await self.client.disconnect()


def send_telegram_code(phone_number, session_name='telegram_session'):
    auth = TelegramAuth(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(auth.send_code_request(phone_number))
        loop.run_until_complete(auth.disconnect())
        return result
    finally:
        loop.close()

def telegram_sign_in(phone_number, code, phone_code_hash, password=None, session_name='telegram_session'):
    auth = TelegramAuth(session_name)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        result = loop.run_until_complete(auth.sign_in(phone_number, code, phone_code_hash, password))
        loop.run_until_complete(auth.disconnect())
        return result
    finally:
        loop.close()

def get_telegram_client(session_name):
    session_dir = os.path.join(os.path.dirname(__file__), 'session')
    os.makedirs(session_dir, exist_ok=True)
    session_path = os.path.join(session_dir, session_name)

    client = TelegramClient(session_path, API_ID, API_HASH)
    return client

def telegram_logout(session_name='telegram_session'):
    try:
        session_dir = os.path.join(os.path.dirname(__file__), 'session')
        session_path = os.path.join(session_dir, session_name + '.session')

        if os.path.exists(session_path):
            try:
                client = get_telegram_client(session_name)

                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_closed():
                        raise RuntimeError("Event loop is closed")
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                async def _logout():
                    try:
                        await client.connect()

                        if await client.is_user_authorized():
                            await client.log_out()

                        await client.disconnect()
                        return True
                    except Exception as e:
                        try:
                            await client.disconnect()
                        except:
                            pass
                        return True

                try:
                    loop.run_until_complete(_logout())
                except Exception as e:
                    print(f"Event loop error: {e}")

                finally:
                    try:
                        if not loop.is_closed():
                            loop.close()
                    except:
                        pass

            except Exception as e:
                print(f"Client error: {e}")

            try:
                os.remove(session_path)
            except Exception as e:
                return False, {'error': f'Failed to delete session file: {str(e)}', 'message': 'Failed to logout'}

        return True, {'message': 'Logged out successfully'}

    except Exception as e:
        return False, {'error': str(e), 'message': 'Failed to logout'}

def get_telegram_user_info(user_id,brand_id):
    """
    Get user information from an existing session
    """
    try:
        session_dir = os.path.join(os.path.dirname(__file__), 'session')
        session_path = os.path.join(session_dir, f'telegram_session_{user_id}_{brand_id}' + '.session')
        
        if not os.path.exists(session_path):
            return {
                'success': False,
                'error': 'Session file not found',
                'message': 'No session file exists for this user'
            }
        
        auth = TelegramAuth(f'telegram_session_{user_id}_{brand_id}')
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def _get_user_info():
                try:
                    await auth.create_client()
                    await auth.client.connect()
                    
                    if not await auth.client.is_user_authorized():
                        return {
                            'success': False,
                            'error': 'Not authorized',
                            'message': 'Session is not authorized'
                        }
                    
                    me = await auth.client.get_me()
                    
                    return {
                        'success': True,
                        'user_id': me.id,
                        'username': me.username,
                        'first_name': me.first_name,
                        'last_name': me.last_name,
                        'phone': me.phone,
                        'message': 'User info retrieved successfully'
                    }
                    
                except Exception as e:
                    return {
                        'success': False,
                        'error': str(e),
                        'message': 'Failed to get user info'
                    }
                finally:
                    await auth.disconnect()
            
            result = loop.run_until_complete(_get_user_info())
            return result
            
        finally:
            loop.close()
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get user info'
        }


