import requests
def get_vimeo_analytics(token,id):
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            likes_url = f"https://api.vimeo.com/videos/{id}/likes"
            comments_url = f"https://api.vimeo.com/videos/{id}/comments"
            video_details_url = f"https://api.vimeo.com/videos/{id}"

            likes_response = requests.get(likes_url, headers=headers)
            comments_response = requests.get(comments_url, headers=headers)
            video_details_response = requests.get(video_details_url, headers=headers)
            

            vimeo_analytics_summary = {
                "total_likes": 0,
                "total_comments": 0,
                "username": ""
            }
            
            if likes_response.status_code == 200:
                likes_data = likes_response.json()
                vimeo_analytics_summary["total_likes"] = likes_data.get('total', 0)

            if comments_response.status_code == 200:
                comments_data = comments_response.json()
                vimeo_analytics_summary["total_comments"] = len(comments_data.get('data', []))

            if video_details_response.status_code == 200:
                video_details_data = video_details_response.json()
                user = video_details_data.get('user', {})
                vimeo_analytics_summary["username"] = user.get('name', "")
            return vimeo_analytics_summary['total_likes'] ,  vimeo_analytics_summary['total_comments'] 