import requests

def get_vimeo_profile(token):
        if not token:
            return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': 'No token provided'}

        profile_url = 'https://api.vimeo.com/me'
        headers = {'Authorization': f'Bearer {token}'}
    
        try:
            response = requests.get(profile_url, headers=headers)
            response.raise_for_status() 
            
            profile_data = response.json()
            return {
                'profile_url': profile_data.get('link', ''),
                'username': profile_data.get('name', ''),
                'name': profile_data.get('name', ''),
                'profile_image': profile_data.get('pictures', {}).get('sizes', [{}])[-1].get('link', '')  
            }
        except requests.HTTPError as http_err:
            return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': f'HTTP error: {http_err}'}
        except Exception as e:
            return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': f'Error: {e}'}
        

def get_vimeo_profile_for_mulitple(token):
        if not token:
            return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': 'No token provided'}

        profile_url = 'https://api.vimeo.com/me'
        headers = {'Authorization': f'Bearer {token}'}
    
        try:
            response = requests.get(profile_url, headers=headers)
            response.raise_for_status() 
            
            profile_data = response.json()
            return profile_data.get('name', '')
        except requests.HTTPError as http_err:
            return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': f'HTTP error: {http_err}'}
        except Exception as e:
            return {'profile_url': None, 'username': None, 'name': None, 'profile_image': None, 'error': f'Error: {e}'}