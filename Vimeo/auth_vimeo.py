import requests
import base64

CLIENT_ID = 'de7f5acf0285537329c3c8c279516caba0f695fa'
CLIENT_SECRET = 'DiafP2nQQFEYHzP+TzubUDyOvsiI/+s4CSnOSWFhwiuP3W4VaG1uEff8Q6VmgtnnB4AeHL/fO5rGh9eP6Mt9i8jcveltZ+G963gT5C8EY4nkKDTWasK9wc43bbkAmWXL'
# CLIENT_ID = '1497b3efc3b02b48c0bdcd6fe75b9c7b80587661'
# CLIENT_SECRET = '5ZCvWjhUFPOxkZCy1JQm5nHC2xDKcWtswefgb4/hvb9MFlrtIP2Q+cGMid/0NiPuPN/jyfxm7/TkAnnmng27W2ccHGJF5BX/fQI/75PkNfh3ro2btoSMfeuywHtv4lty'
# REDIRECT_URI = 'https://staging.flowkar.com/api/vimeo-login/'
# REDIRECT_URI = 'https://dev.flowkar.com/api/vimeo-login/'
REDIRECT_URI = 'https://api.flowkar.com/api/vimeo-login/'
# REDIRECT_URI = 'http://127.0.0.1:8001/api/vimeo-login/'


# def get_user_url_vimeo(user_id):
#     auth_url = f"https://api.vimeo.com/oauth/authorize?response_type=code&client_id={CLIENT_ID}&redirect_uri={REDIRECT_URI}&state={user_id}&scope='public private upload create'"
#     return auth_url


def get_user_url_vimeo(user_id):
    auth_url = f"https://api.vimeo.com/oauth/authorize?response_type=code&client_id={CLIENT_ID}&redirect_uri={REDIRECT_URI}&state={user_id}&scope=public+private+upload+create"
    return auth_url


def code_to_token_vimeo(code):
    credentials = f'{CLIENT_ID}:{CLIENT_SECRET}'
    encoded_credentials = base64.b64encode(credentials.encode()).decode()

    headers = {
        'Authorization': f'Basic {encoded_credentials}',
        'Content-Type': 'application/json'
    }

    data = {
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': REDIRECT_URI
    }

    response = requests.post(
        'https://api.vimeo.com/oauth/access_token', json=data, headers=headers)

    if response.status_code == 200:
        data = response.json()
        token = data['access_token']
        return token
    else:
        return f'Error: {response.status_code}', response.status_code
