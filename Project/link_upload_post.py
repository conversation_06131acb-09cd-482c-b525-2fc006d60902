import os
import requests


def upload_media(access_token, file_path, media_type, owner_urn):
    register_url = 'https://api.linkedin.com/v2/assets?action=registerUpload'
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
        'X-Restli-Protocol-Version': '2.0.0'
    }
    payload = {
        "registerUploadRequest": {
            "recipes": [
                "urn:li:digitalmediaRecipe:feedshare-image"
            ],
            "owner": f'urn:li:person:{owner_urn}',
            "serviceRelationships": [
                {
                    "relationshipType": "OWNER",
                    "identifier": "urn:li:userGeneratedContent"
                }
            ]
        }
    }
    response = requests.post(register_url, json=payload, headers=headers)
    upload_url = response.json()['value']['uploadMechanism']['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest']['uploadUrl']
    asset = response.json()['value']['asset']

    with open(file_path, 'rb') as file:
        upload_response = requests.put(upload_url, data=file, headers={'Authorization': f'Bearer {access_token}', 'Content-Type': media_type})

    return asset

def upload_video_media(access_token, file_path, media_type, owner_urn):
    register_url = 'https://api.linkedin.com/v2/assets?action=registerUpload'
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
        'X-Restli-Protocol-Version': '2.0.0'
    }
    payload = {
        "registerUploadRequest": {
            "recipes": [
                "urn:li:digitalmediaRecipe:feedshare-video"
            ],
            "owner": f'urn:li:person:{owner_urn}',
            "serviceRelationships": [
                {
                    "relationshipType": "OWNER",
                    "identifier": "urn:li:userGeneratedContent"
                }
            ]
        }
    }
    response = requests.post(register_url, json=payload, headers=headers)
    upload_url = response.json()['value']['uploadMechanism']['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest']['uploadUrl']
    asset = response.json()['value']['asset']
    print(f'Assets --> {asset}')
    with open(file_path, 'rb') as file:
        upload_response = requests.put(upload_url, data=file, headers={'Authorization': f'Bearer {access_token}', 'Content-Type': media_type})

    return asset

def create_linkedin_post_with_video(access_token, author_urn, post_text, media_paths, media_types):
    try:
        media_assets = []
        for file_path, media_type in zip(media_paths, media_types):
            media_urn = upload_video_media(access_token, file_path, media_type, author_urn)
            print(f'Media-URN -->{media_urn}')
            media_assets.append({
                "status": "READY",
                "description": {
                    "text": "Sample image description"
                },
                "media": media_urn,
                "title": {
                    "text": "Sample image title"
                }
            })

        url = 'https://api.linkedin.com/v2/ugcPosts'
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0'
        }
        payload = {
            "author": f'urn:li:person:{author_urn}',
            "lifecycleState": "PUBLISHED",
            "specificContent": {
                "com.linkedin.ugc.ShareContent": {
                    "shareCommentary": {
                        "text": post_text
                    },
                    "shareMediaCategory": "VIDEO",
                    "media": media_assets
                }
            },
            "visibility": {
                "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
            }
        }
        
        response = requests.post(url, json=payload, headers=headers)
        if response.status_code == 200 or response.status_code == 201:
            return True , response.json()['id']
        else:
            return False
    except Exception as e :
        print(e)
        return False


def create_linkedin_post_with_multiple_media(access_token, author_urn, post_text, media_paths, media_types):
    try:
        media_assets = []
        for file_path, media_type in zip(media_paths, media_types):
            media_urn = upload_media(access_token, file_path, media_type, author_urn)
            print(f'Media-URN -->{media_urn}')
            media_assets.append({
                "status": "READY",
                "description": {
                    "text": "Sample image description"
                },
                "media": media_urn,
                "title": {
                    "text": "Sample image title"
                }
            })

        url = 'https://api.linkedin.com/v2/ugcPosts'
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'X-Restli-Protocol-Version': '2.0.0'
        }
        payload = {
            "author": f'urn:li:person:{author_urn}',
            "lifecycleState": "PUBLISHED",
            "specificContent": {
                "com.linkedin.ugc.ShareContent": {
                    "shareCommentary": {
                        "text": post_text
                    },
                    "shareMediaCategory": "IMAGE",
                    "media": media_assets
                }
            },
            "visibility": {
                "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
            }
        }
        
        response = requests.post(url, json=payload, headers=headers)
        if response.status_code == 200 or response.status_code == 201:
            return True , response.json()['id']
        else:
            return False 
    except Exception as e :
        print(e)
        return False

# Example usage
# author_urn = 'HuqKyQwk3W'
# access_token = "AQXQRzpJy9rhBi6f49Enav6M8156ElSpm0jiTGev7yq58l4AkqhTgTl7mIrV614DvifX2FYP412Qq-N_GPR9sH5xrQ5s_KoB-tZpwMxRdWyMrR21raILdH35VkXHY0vg4FdpsfujVYcxJFuzzB-urkmXJK9lOdjOCdz9lf01f59CaqO8-s0cB6Se0NlkTXgfblxUx3vCr0-yCFgwd3Fgx7TcChCIqHF3A9hAU2cqDtW6YcGh1hGnqlTZ7rQoHIUZ1WLTM9kvYdkgUupqJ1YBoH-TbU4ZaDN3OuuEhMpoqfNWuvDSdEvp3uIVseJhcE8eqqklmgq_F825hbiReIZjF4cizl0vAQ"
# # print(upload_video_to_account(access_token,'../media/post_files/IMG_8932.MOV',author_urn))
# # # List of image file paths and their media types
# media_paths = ['../media/post_files/IMG_8983.MOV']
# media_types = ['video/MOV']

# post_text = 'This is a test post with multiple images from my LinkedIn API application.'
# post_response = create_linkedin_post_with_video(access_token, author_urn, post_text, media_paths, media_types)

# # Print the response from the LinkedIn API
# print(post_response)