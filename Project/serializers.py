from rest_framework import serializers

from helpers.image_h_w import get_image_dimensions
from .models import *
from django.db.models import Q


def build_https_url(request, url_path):
    """
    Build HTTPS URL for production, HTTP for localhost development
    """
    domain = request.get_host()
    if '127.0.0.1' in domain or 'localhost' in domain:
        protocol = 'http://'
    else:
        protocol = 'https://'
    return f"{protocol}{domain}{url_path}"


class PostFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = PostFiles
        fields = ['file']

class PostSerializer(serializers.ModelSerializer):
    Files = PostFileSerializer(many=True, read_only=True,required=False)
    upload_files = serializers.ListField(
        child=serializers.FileField(allow_empty_file=False, use_url=False),
        write_only=True,
        required=False
    )

    class Meta:
        model = Post
        exclude = ['user','brand']  # Exclude the user field

    def create(self, validated_data):
        uploaded_files = validated_data.pop("upload_files", [])
        user_id = validated_data.pop("user_id")

        post = Post.objects.create(user_id=user_id, **validated_data)

        for file_data in uploaded_files:
            PostFiles.objects.create(post=post, file=file_data)

        return post
    
    def update(self, instance, validated_data):
        uploaded_files = validated_data.pop("upload_files", None)
        if uploaded_files is not None:
            for file_data in uploaded_files:
                PostFiles.objects.create(post=instance, file=file_data)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if isinstance(instance, Post):
            files = instance.postfiles_set.all()
            thumbnail_files = instance.postfilesthumbnail_set.all()
            request = self.context.get('request')
            files_url = [build_https_url(request, file.file.url) for file in files]
            thumbnail_url = [build_https_url(request, file.file.url) for file in thumbnail_files]
            representation['files'] = files_url
            representation['thumbnail_files'] = thumbnail_url
        return representation
    
class PostViewSerializer(serializers.ModelSerializer):

    class Meta:
        model = Post
        fields = ['id','title','description','location','likes','dislikes','comments_count','tagged_in','created_at']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if isinstance(instance, Post):
            files = instance.postfiles_set.all()
            thumbnail_files = instance.postfilesthumbnail_set.all()
            user = instance.user
            request = self.context.get('request')
            files_url = [build_https_url(request, file.file.url) for file in files]
            thumbnail_url = [build_https_url(request, file.file.url) for file in thumbnail_files]
            representation['files'] = files_url
            representation['thumbnail_files'] = thumbnail_url
            isliked = False
            try:
                likes_post = LikePost.objects.get(Q(post_id=instance.pk),Q(user_id=user.pk)) 
                isliked = True
            except:
                isliked = False
            try:
                domain = request.get_host()
                profile_picture = user.profile_picture.url
                if '127.0.0.1' in  domain:
                        header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                profile_picture = ''
                header = ''
                domain = ''

            representation['user'] = {
                'user_id':user.pk,
                'username':user.username,
                'name':user.name,
                'profile_image':f'{header}{domain}{profile_picture}'
                }
            representation['is_liked'] = isliked
            return representation
        
class PostViewScheduleSerializer(serializers.ModelSerializer):

    class Meta:
        model = Post
        fields = ['id','title','description','is_scheduled','scheduled_at','created_at']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if isinstance(instance, Post):
            files = instance.postfiles_set.all()
            thumbnail_files = instance.postfilesthumbnail_set.all()
            user = instance.user
            request = self.context.get('request')
            files_url = [build_https_url(request, file.file.url) for file in files]
            thumbnail_url = [build_https_url(request, file.file.url) for file in thumbnail_files]
            representation['files'] = files_url
            representation['thumbnail_files'] = thumbnail_url
            isliked = False
            try:
                likes_post = LikePost.objects.get(Q(post_id=instance.pk),Q(user_id=user.pk)) 
                isliked = True
            except:
                isliked = False
            try:
                domain = request.get_host()
                profile_picture = user.profile_picture.url
                if '127.0.0.1' in  domain:
                        header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                profile_picture = ''
                header = ''
                domain = ''

            representation['user'] = {
                'user_id':user.pk,
                'username':user.username,
                'name':user.name,
                'profile_image':f'{header}{domain}{profile_picture}'
                }
            representation['is_liked'] = isliked
            return representation
        
class PostViewAdminSerializer(serializers.ModelSerializer):

    class Meta:
        model = Post
        fields = ['id','title','description','location','likes','dislikes','comments_count','tagged_in','facebook','instagram','linkedin','pinterest','vimeo','youtube','dailymotion','reddit','tumblr','twitter','created_at']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if isinstance(instance, Post):
            files = instance.postfiles_set.all()
            user = instance.user
            request = self.context.get('request')
            files_url = [build_https_url(request, file.file.url) for file in files]
            # representation['files'] = files_url

            width, height = (0, 0)
            if files_url:
                try:
                    width, height = get_image_dimensions(files_url[0])
                except Exception as e:
                    print(f"Error fetching image dimensions: {e}")
            
            representation['files'] = files_url
            representation['width'] = width
            representation['height'] = height


            thumbail_files = []
            post_files = files.filter(is_video=True).prefetch_related('postfilesthumbnail_set')
            for file in post_files:
                thumbnails = file.postfilesthumbnail_set.all()
                thumbail_files.extend([build_https_url(request, thumbnail.file.url) for thumbnail in thumbnails])
            representation['thumbail_files'] = thumbail_files

            
            try:
                domain = request.get_host()
                profile_picture = user.profile_picture.url
                if '127.0.0.1' in  domain:
                        header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                profile_picture = ''
                header = ''
                domain = ''
            representation['user'] = {
                'user_id':user.pk,
                'username':user.username,
                'name':user.name,
                'profile_image':f'{header}{domain}{profile_picture}'
                }
            return representation
        
class PostViewUserSerializer(serializers.ModelSerializer):

    class Meta:
        model = Post
        fields = ['id']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if isinstance(instance, Post):
            files = instance.postfiles_set.all()
            user = instance.user
            request = self.context.get('request')
            files_url = [build_https_url(request, file.file.url) for file in files]
            representation['files'] = files_url
        
            try:
                domain = request.get_host()
                profile_picture = user.profile_picture.url
                if '127.0.0.1' in  domain:
                        header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                profile_picture = ''
                header = ''
                domain = ''
            # representation['user'] = {
            #     'user_id':user.pk,
            #     'username':decrypt_data(user.username),
            #     'name':decrypt_data(user.name),
            #     'profile_image':f'{header}{domain}{profile_picture}'
            #     }
            return representation
        
class StoryViewSerializer(serializers.ModelSerializer):

    class Meta:
        model = Story
        fields = ['id','user']  # Exclude the user field

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if isinstance(instance, Story):
            files = instance.storyfiles_set.all()
            user = instance.user
            request = self.context.get('request')
            files_url = [build_https_url(request, file.file.url) for file in files]
            representation['files'] = files_url
        
            try:
                domain = request.get_host()
                profile_picture = user.profile_picture.url
                if '127.0.0.1' in  domain:
                        header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                profile_picture = ''
                header = ''
                domain = ''

            representation['user'] = {
                'user_id':user.pk,
                'username':user.username,
                'name':user.name,
                'profile_image':f'{header}{domain}{profile_picture}'
                }
            return representation
        
class PostLikeListSerializer(serializers.ModelSerializer):

    class Meta:
        model = LikePost
        fields = '__all__'

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if isinstance(instance, LikePost):
            user = instance.user
            request = self.context.get('request')
            try:
                domain = request.get_host()
                profile_picture = user.profile_picture.url
                if '127.0.0.1' in  domain:
                        header = 'https://'
                else:
                    header = 'https://'
            except ValueError:
                profile_picture = ''
                header = ''
                domain = ''

            representation['user'] = {
                'user_id':user.pk,
                'username':user.username,
                'name':user.name,
                'profile_image':f'{header}{domain}{profile_picture}'
                }
            print(f"Representation: {representation}")
            return representation