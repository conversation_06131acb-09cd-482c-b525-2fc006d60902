
import os
import json
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from datetime import datetime, timedelta
import requests
from collections import defaultdict


def get_youtube_analytics(file_path, start_date, end_date):


    try:
        with open(file_path, 'r') as file:
            credentials_data = json.load(file)

        credentials = Credentials(
            token=credentials_data['access_token'],
            refresh_token=credentials_data.get('refresh_token'),
            token_uri=credentials_data['token_uri'],
            client_id=credentials_data['client_id'],
            client_secret=credentials_data['client_secret'],
            scopes=credentials_data['scopes']
        )

        os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
        youtubeAnalytics = build('youtubeAnalytics', 'v2', credentials=credentials)

        response = youtubeAnalytics.reports().query(
            ids='channel==MINE',
            startDate=start_date,
            endDate=end_date,
            metrics='views,comments,likes,dislikes,estimatedMinutesWatched,averageViewDuration,subscribersGained,subscribersLost,shares',
            dimensions='day',
            sort='day'
        ).execute()

        # Convert response to dictionary format
        data_dict = {row[0]: {
            "views": row[1],
            "comments": row[2],
            "likes": row[3],
            "dislikes": row[4],
            "estimatedMinutesWatched": row[5],
            "averageViewDuration": row[6],
            "subscribersGained": row[7],
            "subscribersLost": row[8],
            "shares": row[9]
        } for row in response.get('rows', [])}

        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        all_dates = [(start + timedelta(days=i)).strftime("%Y-%m-%d") for i in range((end - start).days + 1)]

        default_values = {
            "views": 0,
            "comments": 0,
            "likes": 0,
            "dislikes": 0,
            "estimatedMinutesWatched": 0,
            "averageViewDuration": 0,
            "subscribersGained": 0,
            "subscribersLost": 0,
            "shares": 0
        }

        # Ensure data is only within the requested range
        complete_data = {date: data_dict.get(date, default_values) for date in all_dates}

        # Calculate the total for the given range
        total_metrics = {
            "views": sum(values["views"] for values in complete_data.values()),
            "comments": sum(values["comments"] for values in complete_data.values()),
            "likes": sum(values["likes"] for values in complete_data.values()),
            "dislikes": sum(values["dislikes"] for values in complete_data.values()),
            "estimatedMinutesWatched": sum(values["estimatedMinutesWatched"] for values in complete_data.values()),
            "averageViewDuration": sum(values["averageViewDuration"] for values in complete_data.values()),
            "subscribersGained": sum(values["subscribersGained"] for values in complete_data.values()),
            "subscribersLost": sum(values["subscribersLost"] for values in complete_data.values()),
            "shares": sum(values["shares"] for values in complete_data.values()),
        }

        formatted_response = {
            # "kind": response.get("kind", "youtubeAnalytics#resultTable"),
            # "columnHeaders": response.get("columnHeaders", []),
            "analytics": [{"date": date, **values} for date, values in complete_data.items()],
            "total": total_metrics  
        }

        return True, formatted_response
    except Exception as e:
        return False, {"error": str(e)}









# # for video count
def get_access_token(json_file):
    with open(json_file, "r") as file:
        creds = json.load(file)

    response = requests.post("https://oauth2.googleapis.com/token", data={
        "client_id": creds["client_id"],
        "client_secret": creds["client_secret"],
        "refresh_token": creds["refresh_token"],
        "grant_type": "refresh_token"
    })

    return response.json().get("access_token") if response.status_code == 200 else None

def get_channel_id(json_file):
    access_token = get_access_token(json_file)
    if not access_token:
        return None

    response = requests.get("https://www.googleapis.com/youtube/v3/channels?part=id&mine=true", headers={
        "Authorization": f"Bearer {access_token}"
    })

    if response.status_code == 200:
        return response.json().get("items", [{}])[0].get("id")
    return None

def get_videos_count(file_path, start_date, end_date):
    try:
        channel_id = get_channel_id(file_path)
        if not channel_id:
            return {"error": "Failed to fetch Channel ID"}

        with open(file_path, 'r') as file:
            credentials_data = json.load(file)

        credentials = Credentials(
            token=credentials_data['access_token'],
            refresh_token=credentials_data.get('refresh_token'),
            token_uri=credentials_data['token_uri'],
            client_id=credentials_data['client_id'],
            client_secret=credentials_data['client_secret'],
            scopes=credentials_data['scopes']
        )

        youtube = build('youtube', 'v3', credentials=credentials)

        # Initialize date dictionary with 0 counts
        video_count_by_date = defaultdict(int)
        
        # Fill dictionary with all dates in range set to 0
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        while start_dt <= end_dt:
            video_count_by_date[start_dt.strftime("%Y-%m-%d")] = 0
            start_dt += timedelta(days=1)

        next_page_token = None

        while True:
            request = youtube.search().list(
                part="snippet",
                channelId=channel_id,
                publishedAfter=f"{start_date}T00:00:00Z",
                publishedBefore=f"{end_date}T23:59:59Z",
                type="video",
                maxResults=50, 
                pageToken=next_page_token
            )
            response = request.execute()

            for item in response.get("items", []):
                published_date = item["snippet"]["publishedAt"].split("T")[0]
                if published_date in video_count_by_date:
                    video_count_by_date[published_date] += 1  

            next_page_token = response.get("nextPageToken")
            if not next_page_token:
                break

        total_videos = sum(video_count_by_date.values())

        return True,{
            "video_count_by_date": dict(video_count_by_date),
            "total_videos": total_videos
        }

    except Exception as e:
        return False,{}



def get_subscriber_count(json_file):
    access_token = get_access_token(json_file)
    if not access_token:
        return None

    response = requests.get("https://www.googleapis.com/youtube/v3/channels?part=statistics&mine=true", headers={
        "Authorization": f"Bearer {access_token}"
    })
    
    if response.status_code == 200:
        return response.json().get("items", [{}])[0].get("statistics", {}).get("subscriberCount")
    return None

# # Usage
# file_path = "13-oauth2.json"
# start_date = "2024-09-25"
# end_date = "2025-03-25"

# print(get_videos_count(file_path, start_date, end_date))



# file_path = "rjs.json"
# start_date = "2024-09-25"
# end_date = "2025-03-20"
# data = get_youtube_analytics(file_path, start_date, end_date)

