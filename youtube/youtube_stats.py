import json
import os
import requests
from helpers.refresh_to_access import get_access_token_from_refresh_token


# def get_youtube_stats(json_file,video_id,refresh_token):
#     url = f'https://www.googleapis.com/youtube/v3/videos?part=statistics&id={video_id}'
#     access_token = get_access_token_from_refresh_token(json_file,refresh_token)
#     print(f'access_token : {access_token}')
#     headers = {
#         "Authorization": f"Bearer {access_token}"
#     }
#     get_request = requests.get(url,headers=headers)
#     get_request_response = get_request.json()
#     if get_request.status_code == 200:
#         print(get_request_response)
#         likes = get_request_response['items'][0]['statistics']['likeCount']
#         dislikes = get_request_response['items'][0]['statistics']['dislikeCount']
#         comments = get_request_response['items'][0]['statistics']['commentCount']
#         views = get_request_response['items'][0]['statistics']['viewCount']
#         return likes,comments
#     else:
#         return 0,0
    

def get_youtube_stats(json_file, video_id, refresh_token):

    if not os.path.exists(json_file):
        return 0, 0  

    try:
        access_token = get_access_token_from_refresh_token(json_file, refresh_token)
        url = f'https://www.googleapis.com/youtube/v3/videos?part=statistics&id={video_id}'
        headers = {
            "Authorization": f"Bearer {access_token}"
        }
        response = requests.get(url, headers=headers)
        response_data = response.json()

        if response.status_code == 200 and response_data.get('items'):
            stats = response_data['items'][0]['statistics']
            likes = stats.get('likeCount', 0)
            comments = stats.get('commentCount', 0)
            return int(likes), int(comments)
        else:
            return 0, 0
    except Exception as e:
        return 0, 0



def get_access_token(json_file):
    with open(json_file, "r") as file:
        creds = json.load(file)

    response = requests.post("https://oauth2.googleapis.com/token", data={
        "client_id": creds["client_id"],
        "client_secret": creds["client_secret"],
        "refresh_token": creds["refresh_token"],
        "grant_type": "refresh_token"
    })

    return response.json().get("access_token") if response.status_code == 200 else None

def get_channel_id(json_file,refresh_token):

    with open(json_file, "r") as file:
        creds = json.load(file)


    response = requests.post("https://oauth2.googleapis.com/token", data={
            "client_id": creds["client_id"],
            "client_secret": creds["client_secret"],
            "refresh_token": creds["refresh_token"] if creds["refresh_token"] else refresh_token ,
            "grant_type": "refresh_token"
        })
    
    access_token = response.json().get("access_token")

    if not access_token:
        return "Failed to get access token"

    response = requests.get("https://www.googleapis.com/youtube/v3/channels?part=id&mine=true", headers={
        "Authorization": f"Bearer {access_token}"
    })

    return response.json().get("items", [{}])[0].get("id", "") if response.status_code == 200 else ""

def get_channel_details(json_file,refresh_token):
    try:

        with open(json_file, "r") as file:
            creds = json.load(file)


        response = requests.post("https://oauth2.googleapis.com/token", data={
                "client_id": creds["client_id"],
                "client_secret": creds["client_secret"],
                "refresh_token": creds["refresh_token"] if creds["refresh_token"] else refresh_token ,
                "grant_type": "refresh_token"
            })
        
        access_token = response.json().get("access_token")


        response = requests.get("https://www.googleapis.com/youtube/v3/channels?part=id,snippet&mine=true", headers={
            "Authorization": f"Bearer {access_token}"
        })

        data = response.json()
        if "items" in data and len(data["items"]) > 0:
                channel = data["items"][0]
                channel_id = channel.get("id", "")
                title = channel["snippet"].get("title", "")
                description = channel["snippet"].get("description", "")
                customUrl = channel["snippet"].get("customUrl", "")
                profile_image = channel["snippet"]["thumbnails"].get("high", {}).get("url", "")

                return True ,{
                    "channel_id": channel_id,
                    "title": title,
                    "description": description,
                    "profile_image": profile_image,
                    "customUrl":customUrl
                }
        else:
            return False,{
                    "channel_id": "",
                    "title": "",
                    "description": "",
                    "profile_image": "",
                    "customUrl":""
                }
    except AttributeError:
            return False,{
                    "channel_id": "",
                    "title": "",
                    "description": "",
                    "profile_image": "",
                    "customUrl":""
                }

def get_channel_id_without_access_token(json_file):
    with open(json_file, "r") as file:
        creds = json.load(file)

    access_token = creds.get("access_token")
    

    if not access_token:
        return "Failed to get access token"

    response = requests.get("https://www.googleapis.com/youtube/v3/channels?part=id&mine=true", headers={
        "Authorization": f"Bearer {access_token}"
    })

    return response.json().get("items", [{}])[0].get("id", "") if response.status_code == 200 else ""

# file = './1-oauth2.json'

# print(get_channel_id(file,"1//0g_LSA-GoCdglCgYIARAAGBASNwF-L9IriemjvevLfqlYzPvfki-Go0QIZglPwUdWzcZfPU4vDt2zX6FeEQKpXCsGLRMQ9rASk0o"))