import httplib2
import os
import random
import sys
import time

from apiclient.discovery import build # type: ignore
from apiclient.errors import HttpError # type: ignore
from apiclient.http import MediaFileUpload # type: ignore
from oauth2client.client import flow_from_clientsecrets
from oauth2client.file import Storage
from oauth2client.tools import argparser, run_flow
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
import httplib2
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from oauth2client.file import Storage
from oauth2client.client import flow_from_clientsecrets, OAuth2WebServerFlow



CLIENT_SECRETS_FILE = "../youtube/client_secrets.json"
YOUTUBE_UPLOAD_SCOPE = ["https://www.googleapis.com/auth/youtube.upload","https://www.googleapis.com/auth/youtube.readonly","https://www.googleapis.com/auth/yt-analytics.readonly"]
YOUTUBE_API_SERVICE_NAME = "youtube"
YOUTUBE_API_VERSION = "v3"
VALID_PRIVACY_STATUSES = ("public", "private", "unlisted")
    
# REDIRECT_URI = 'https://staging.yooii.com/api/youtube/'
# REDIRECT_URI = 'https://staging.flowkar.com/api/youtube/'
# REDIRECT_URI = 'https://dev.flowkar.com/api/youtube/'
REDIRECT_URI = 'https://api.flowkar.com/api/youtube/'
# REDIRECT_URI = 'http://localhost:8001/api/youtube/'


def get_auth_url(user_id):
    state = f"user_id:{user_id}"
    flow = OAuth2WebServerFlow(
        client_id='732230002227-3ueqt1buqfbffcrfrjv4slinhpths2im.apps.googleusercontent.com',
        client_secret='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',
        scope=YOUTUBE_UPLOAD_SCOPE,
        redirect_uri=REDIRECT_URI,
        state=user_id,
        access_type="offline",
        prompt="consent",
        include_granted_scopes="true"  # Ensures all requested scopes are granted
    )
    auth_url = flow.step1_get_authorize_url()
    return auth_url

def authenticate_user(name, auth_code):
    storage_filename = f'{name}-oauth2.json'
    flow = OAuth2WebServerFlow(
        client_id='732230002227-3ueqt1buqfbffcrfrjv4slinhpths2im.apps.googleusercontent.com',
        client_secret='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',
        scope=YOUTUBE_UPLOAD_SCOPE,
        redirect_uri=REDIRECT_URI,
        access_type="offline",
        prompt="consent",
        include_granted_scopes="true"
    )

    storage = Storage(storage_filename)
    credentials = flow.step2_exchange(auth_code)
    storage.put(credentials)

    return build(YOUTUBE_API_SERVICE_NAME, YOUTUBE_API_VERSION, http=credentials.authorize(httplib2.Http()))

def get_authenticated_service(name):
    storage_filename = f'{name}-oauth2.json'

    if os.path.exists(storage_filename):
        storage = Storage(storage_filename)
        credentials = storage.get()
        if credentials is None or credentials.invalid:
            return f"Please visit this URL to authorize the application: {get_auth_url()}"

        return build(YOUTUBE_API_SERVICE_NAME, YOUTUBE_API_VERSION,
                     http=credentials.authorize(httplib2.Http()))
    else:
        return f"Please visit this URL to authorize the application: {get_auth_url()}"
    
