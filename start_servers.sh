#!/bin/bash

# Start the Django server in the background
echo "Starting Django server..."
nohup python manage.py runserver 2001 > django.log 2>&1 &
DJANGO_PID=$!

sleep 5

# Start the Flask server in the background
echo "Starting Flask server..."
nohup python time_script.py > flask.log 2>&1 &
FLASK_PID=$!

sleep 3

echo "Sending request to Flask server..."
# curl http://127.0.0.1:5002/start

# Function to handle stopping servers
stop_servers() {
    echo "Stopping servers..."
    kill $DJANGO_PID $FLASK_PID
    wait $DJANGO_PID $FLASK_PID
    echo "Servers stopped."
    exit
}

# Trap Ctrl+C and call stop_servers function
trap stop_servers INT

# Wait indefinitely
echo "Servers are up and running. Press Ctrl+C to stop."
while true; do
    sleep 60
done
