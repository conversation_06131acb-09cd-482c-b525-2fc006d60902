from pymongo import ReturnDocument
from pymongo.errors import DuplicateKeyError
from pymongo.server_api import Server<PERSON><PERSON>
from pymongo.mongo_client import MongoClient
from bson import ObjectId

from datetime import datetime, timedelta


MONGO_URL = 'mongodb://Flowkar:Dgl14tP%3Dz%5DG%5E%3D-a1%40-5Che8_%24@157.173.218.248:27017/admin'

client = MongoClient(MONGO_URL, server_api=ServerApi('1'))

try:
    client.admin.command('ping')
    print("Pinged your deployment. You successfully connected to MongoDB!")
except Exception as e:
    print(e)


# Access the database and collection
db = client.db_flowkar_prod
UserRegistrationCollection = db.UserRegistration
LoginActivityConnection = db.LoginActivityConnection
PointsTableCollection = db.PointsTable
PostCollection =  db.Post
UserPointsCollection = db.UserPoints
UserSocialConnectionCollection = db.UserSocialConnection
UserRewardTransactionCollection = db.UserRewardTransaction
UserCoinWalletCollection = db.UserCoinWallet
PricePoolCollection = db.PricePool

def db_register_user(data):
    try:
        data['live_socket_id'] = ""
        create = UserRegistrationCollection.insert_one(data)
    except DuplicateKeyError:
        pass

    return True

def db_update_user_data(user_id,username,name,profile_picture):
    try:
        update = UserRegistrationCollection.find_one_and_update({"_id":user_id},{"$set":{"username":username,"name":name,'profile_picture':profile_picture}})
    except DuplicateKeyError:
        pass

    return True

def db_get_user(pk):
    user = UserRegistrationCollection.find_one({"_id": int(pk)})
    return user

def db_create_or_update_login_activity(user_id,last_login):
    try:
        create = LoginActivityConnection.insert_one({'_id':int(user_id),'last_login':last_login,'seven_day_active':False,'is_profile_complete':True,'log_data':[{'date_time':last_login}]})
        return True
    except DuplicateKeyError:
        user_data = LoginActivityConnection.find_one({'_id': int(user_id)})
        
        if user_data:
            latest_login = user_data['log_data'][-1]['date_time']
            
            if last_login > latest_login + timedelta(hours=4):
                LoginActivityConnection.update_one(
                    {'_id': int(user_id)},
                    {'$push': {'log_data': {'date_time': last_login}}}
                )
                return True
        return True
    
def db_update_user_profile_status(user_id):
    try:
        update = LoginActivityConnection.find_one_and_update({"_id":user_id},{"$set":{"is_profile_complete":True}})
        return True
    except Exception:
        return False
    
def db_get_user_profile_status(user_id):
    try:
        data = LoginActivityConnection.find_one({"_id":user_id})
        return data['is_profile_complete']
    except Exception:
        return False
    
def db_update_user_activity_status(user_id):
    try:
        update = LoginActivityConnection.find_one_and_update({"_id":user_id},{"$set":{"seven_day_active":True}})
        return True
    except Exception:
        return False
    
def db_get_user_activity_status_7(user_id):
    try:
        data = LoginActivityConnection.find_one({"_id":user_id})
        return  data['seven_day_active'] 
    except Exception:
        return False
    
def db_get_user_login_activity(user_id):
    data = LoginActivityConnection.find_one({'_id':int(user_id)})
    return data

def db_get_active_inactive_users():
    # Calculate 30 days ago
    thirty_days_ago = datetime.now() - timedelta(days=7)

    # Get all login activity records
    all_login_activities = list(LoginActivityConnection.find({}))

    active_users = 0
    inactive_users = 0

    for activity in all_login_activities:
        if 'log_data' in activity and activity['log_data']:

            latest_login = None
            for log_entry in activity['log_data']:
                if 'date_time' in log_entry:
                    login_time = log_entry['date_time']
                    if isinstance(login_time, str):
                        try:
                            login_time = datetime.fromisoformat(login_time.replace('Z', '+00:00'))
                        except:
                            continue

                    if latest_login is None or login_time > latest_login:
                        latest_login = login_time

            # Check if user is active (logged in within last 30 days)
            if latest_login and latest_login >= thirty_days_ago:
                active_users += 1
            else:
                inactive_users += 1
        else:
            # No login data means inactive
            inactive_users += 1

    return {
        'active_users': active_users,
        'inactive_users': inactive_users
    }
    
def db_create_points():
    try:
        create = PointsTableCollection.insert_one({'_id':1,'points':{
                'daily_login':[5,0],
                'completing_profile':[20,0],
                "seven_day_active":[100,0],
                'follow':[5,5],
                'like':[5,5],
                'comment':[5,5],
                'sharing_internal_post':[10,5],
                'sharing_external_post':[15,10],
                "social_connect":[30,0],
                "social_post_upload":[4,0],
                "upload_post":[20,0],
                "invite_user":[30,30],
                "invite_user_premium":[20,0],
                "800_points":[50,0],
                "1200_points":[100,0],
                "top_10_weekly":[100,0],
                "refferal_leader_monthly":[200,0],

        }})
        return True
    except DuplicateKeyError:
        pass

def db_get_points(arg):
    data = PointsTableCollection.find_one({'_id':1})
    return data.get("points")[arg] if data else []

def db_create_post(post_id):
    try:
        create = PostCollection.insert_one({'_id':post_id,'likes':0,'shares':0,'comments':0})
        return True
    except DuplicateKeyError:
        pass

def db_update_post(post_id,likes,shares,comments):
        update = PostCollection.find_one_and_update({'_id':post_id},{'$inc':{'likes':likes,'shares':shares,'comments':comments}})
        if update is None:
            return False
        return True
    
def db_create_user_points(user_id):
    try:
        create = UserPointsCollection.insert_one({'_id':int(user_id),'points':0,'coins':0,'level_1':False,'level_2':False})
        return True
    except DuplicateKeyError:
        return False
    
def db_get_user_points(user_id):
    try:
        data = UserPointsCollection.find_one({'_id':int(user_id)})
        return data['points']
    except DuplicateKeyError:
        return 0
    
def db_get_user_coins(user_id):
    try:
        data = UserPointsCollection.find_one({'_id':int(user_id)})
        return data['coins']
    except :
        return 0
    
def db_get_user_points_data(user_id):
    try:
        data = UserPointsCollection.find_one({'_id':int(user_id)})
        return data
    except DuplicateKeyError:
        return 0
    
def db_update_points(user_id,points,transaction_type):
        update = UserPointsCollection.find_one_and_update({'_id':int(user_id)},{'$inc':{'points':points}})
        if update is None:
            return False
        db_create_user_reward_transaction(user_id,points,transaction_type)
        return True


def db_update_level(user_id,position):
        if position == 1:
            update = UserPointsCollection.find_one_and_update({'_id':int(user_id)},{'$set':{'level_1':True}})
        else:
            update = UserPointsCollection.find_one_and_update({'_id':int(user_id)},{'$set':{'level_2':True}})

        if update is None:
            return False
        return True

def db_create_user_social(brand_id):
    try:
        create = UserSocialConnectionCollection.insert_one({'_id':int(brand_id),"instagram":False,"facebook":False,"youtube":False,"linkedin":False,"pinterest":False,"vimeo":False,'tiktok':False,"tumblr":False,"reddit":False,"threads":False})
        return True
    except DuplicateKeyError:
        pass

def db_update_user_social(brand_id, **kwargs):
    allowed_keys = {"instagram", "facebook", "youtube", "linkedin", "pinterest", "vimeo", "tiktok", "tumblr", "reddit","threads"}
    update_fields = {key: value for key, value in kwargs.items() if key in allowed_keys}

    if not update_fields:
        return False  # No valid fields to update

    result = UserSocialConnectionCollection.update_one(
        {'_id': int(brand_id)},
        {'$set': update_fields}
    )
    
    return result.modified_count > 0

def db_get_user_social(brand_id,key):
    try:
        data = UserPointsCollection.find_one({'_id': int(brand_id)})
        return data[key]
    except Exception:
        return {}
    
def db_create_user_reward_transaction(user_id,points,transaction_type):
    try:
        existing_user = UserRewardTransactionCollection.find_one({'_id': int(user_id)})
        
        if existing_user is None:
            create = UserRewardTransactionCollection.insert_one({
                '_id': int(user_id),
                'transaction': [{'points': points, 'transaction_type': transaction_type}]
            })
        else:
            update = UserRewardTransactionCollection.update_one(
                {'_id': int(user_id)},
                {'$push': {'transaction': {'points': points, 'transaction_type': transaction_type}}}
            )
        return True
    except Exception as e:
        print(f"Error in db_create_user_reward_transaction: {e}")
        return False

def db_get_user_reward_transaction(user_id):
    try:
        data = UserRewardTransactionCollection.find_one({'_id': int(user_id)})
        return data['transaction']
    except Exception:
        return []
    
def db_get_leaderboard_data():
    try:
        data = UserPointsCollection.find().sort('points', -1).limit(103)
        return data
    except Exception:
        return []



#Price Pool

def create_price_pool():
    try:
        create = PricePoolCollection.insert_one({'_id':1,'max_pool_amount':1000000000,'pool_per_user':100})
        return True
    except DuplicateKeyError:
        pass

def get_price_pool():
    try:
        data = PricePoolCollection.find_one({'_id':1})
        return data
    except Exception:
        return {}
    
def get_live_users():
    try:
        data = UserRegistrationCollection.find({
            'live_socket_id': {'$ne': None, '$exists': True, '$ne': ''}
        })
        return data
    except Exception:
        return []
    


def get_user_transaction_counts_flexible(user_id):
    counts = {
        "like": 0,
        "comment": 0, 
        "follow": 0,
        "share": 0
    }
    
    keyword_mappings = {
        "like": ["like"],
        "comment": ["comment"], 
        "follow": ["follow"],
        "share": ["post share", "share"]
    }
    
    try:
        query = {
            "_id": int(user_id),
            "transaction.1": {"$exists": True}
        }
        
        cursor = UserRewardTransactionCollection.find(query)
        
        for doc in cursor:
            if 'transaction' in doc:
                # Iterate through ALL transactions, not just the second one
                for transaction in doc['transaction']:
                    if 'transaction_type' in transaction:
                        transaction_type = transaction['transaction_type'].lower()
                        
                        for category, keywords in keyword_mappings.items():
                            if any(keyword in transaction_type for keyword in keywords):
                                if category == "like" and counts[category] >= 20:
                                    continue
                                elif category == "comment" and counts[category] >= 20:
                                    continue
                                elif category == "follow" and counts[category] >= 20:
                                    continue
                                elif category == "share" and counts[category] >= 20:
                                    continue
                                else:
                                    counts[category] += 1
                                break
        
        return counts
        
    except Exception as e:
        print(f"Error occurred: {e}")
        return counts


create_price_pool()
db_create_points()