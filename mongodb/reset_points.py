from pymongo import MongoClient
from pymongo.server_api import ServerApi
import numpy as np
from datetime import datetime

# MongoDB connection
MONGO_URL = 'mongodb://Flowkar:Dgl14tP%3Dz%5DG%5E%3D-a1%40-5Che8_%24@157.173.218.248:27017/admin'


def distribute_coins_by_rank(total_pool_amount, total_users):
    """
    Distribute coins based on user rankings using a weighted distribution system.
    Higher ranked users get more coins, with the distribution following a geometric progression.
    """
    # Create a geometric progression for distribution
    # The ratio between consecutive ranks will be 0.8 (20% decrease per rank)
    ratio = 0.8
    weights = [ratio ** i for i in range(total_users)]
    total_weight = sum(weights)

    # Calculate coins per weight unit
    coins_per_weight = total_pool_amount / total_weight

    # Calculate coins for each rank
    distribution = [int(weight * coins_per_weight) for weight in weights]

    # Adjust the last few ranks to ensure we don't give too few coins
    min_coins = 1
    for i in range(len(distribution)-1, -1, -1):
        if distribution[i] < min_coins:
            distribution[i] = min_coins

    return distribution


def reset_all_user_points():
    try:
        # Connect to MongoDB
        client = MongoClient(MONGO_URL, server_api=ServerApi('1'))
        db = client.db_flowkar_prod
        UserPointsCollection = db.UserPoints
        UserPointsTransaction = db.UserRewardTransaction
        PricePoolCollection = db.PricePool

        total_user = UserPointsCollection.count_documents({})
        pool_data = PricePoolCollection.find_one({'_id': 1})
        total_pool_per_user = pool_data['pool_per_user']
        total_pool_amount = pool_data['max_pool_amount']
        leaderboard = list(UserPointsCollection.find().sort('points', -1))

        if total_user * total_pool_per_user > total_pool_amount:
            # Get all users sorted by points in descending order

            # Calculate coin distribution based on rankings
            coin_distribution = distribute_coins_by_rank(
                total_pool_amount, total_user)
            UserPointsTransaction.update_many(
                {},  # match all documents
                {'$set': {'transaction': []}}
            )
            # Update user points and distribute coins
            for i, user in enumerate(leaderboard):
                coins_to_give = coin_distribution[i]
                UserPointsCollection.update_one(
                    {'_id': user['_id']},
                    {
                        '$set': {'points': 0},
                        '$inc': {'coins': coins_to_give}
                    }
                )

                # Add transaction record
                UserPointsTransaction.update_one(
                    {'_id': user['_id']},
                    {'$push': {'transaction': {
                        'transaction_type': 'Rank Reward',
                        'points': coins_to_give,
                        'rank': i + 1,
                        'timestamp': datetime.utcnow()
                    }}}
                )

            print(
                f"Successfully distributed coins to {total_user} users based on rankings")
            return total_user
        else:
            distribution_amount = total_user*total_pool_per_user
            coin_distribution = distribute_coins_by_rank(
                distribution_amount, total_user)
            UserPointsTransaction.update_many(
                {},  # match all documents
                {'$set': {'transaction': []}}  # set empty array for all
            )

            # Update user points and distribute coins
            for i, user in enumerate(leaderboard):
                coins_to_give = coin_distribution[i]
                UserPointsCollection.update_one(
                    {'_id': user['_id']},
                    {
                        '$set': {'points': 0},
                        '$inc': {'coins': coins_to_give}
                    }
                )

                # Add transaction record
                UserPointsTransaction.update_one(
                    {'_id': user['_id']},
                    {'$push': {'transaction': {
                        'transaction_type': 'Rank Reward',
                        'points': coins_to_give,
                        'rank': i + 1,
                        'timestamp': datetime.utcnow()
                    }}}
                )

            print(
                f"Successfully distributed coins to {total_user} users based on rankings")
            return total_user
    except Exception as e:
        print(f"Error in reset_all_user_points: {e}")
        return 0
    finally:
        client.close()


if __name__ == "__main__":
    reset_all_user_points()
