import requests


def get_linkedin_profile(token):
        if not token:
            return {'profile_url': '', 'username': '', 'name': '', 'profile_picture': '', 'error': 'No token provided'}
    
        profile_url = 'https://api.linkedin.com/v2/userinfo'
        public_sharing = 'https://api.linkedin.com/v2/me'
        headers = {'Authorization': f'Bearer {token}'}

        try:
            response = requests.get(profile_url, headers=headers)
            public_response = requests.get(public_sharing, headers=headers)
            response.raise_for_status()  

            public_data = public_response.json()
            profile_data = response.json()
            return {
                'profile_url': f"https://www.linkedin.com/in/{public_data.get('vanityName', '')}",
                'username': profile_data.get('given_name', ''),
                'name': f"{profile_data.get('given_name', '')} {profile_data.get('family_name', '')}",
                'profile_picture': profile_data.get('picture', '')
            }
        except requests.HTTPError as http_err:
            return {'profile_url': '', 'username': '', 'name': '', 'profile_picture': '', 'error': f'HTTP error: {http_err}'}
        except Exception as e:
            return {'profile_url': '', 'username': '', 'name': '', 'profile_picture': '', 'error': f'Error: {e}'}