import requests

def get_linkedin_functions(access_token, locale=None):
    url = "https://api.linkedin.com/v2/functions"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    params = {}
    if locale:
        params["locale"] = locale

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as err:
        return {"error": str(err), "status_code": response.status_code}
    except Exception as e:
        return {"error": str(e)}

access_token = 'AQVLgITJqy1V9jhRdO_WYRmzotWzn9LKarUJuxgNrbBbd0jDK5R43aDp29yLCWKdbzJpIAvgB6S1C2PfuazosBEgJtaMPZYjXHT7aYFbwpU9pyfVfkT6a9-pR1KWhcj40ygw7SpSl_8G3y-pr8h9TwyL9BxSCWyjtmug77iqWBL56WtyHcmgoupKTdA5nRLcC8FxUyvvHwPgOfxLp_xLikkPQnvf4eYdmm0VS_2Ce0t0pucc2gdmxKfo9b4cSxKYlzYibmHCi1Ig523dKwuXwAcxCd6Y7YNAjk-gBi2o9McUA3ObR6D2nnr3cwzObTDsmTA70gxr_JUy3oNHJLKvwk1M9WzT0Q'

data = get_linkedin_functions(access_token, locale='en_US')

if 'elements' in data:
    print("LinkedIn Functions:")
    for item in data['elements']:
        name = item['name']['localized'].get('en_US', 'N/A')
        function_id = item.get('id', 'N/A')
        print(f"- {name} (ID: {function_id})")
else:
    print("Error:", data.get('error'))
