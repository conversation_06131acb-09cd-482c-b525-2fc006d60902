import requests

def linked_in_analytics_likes(token,id):
            urn_id = id
            social_actions_url = f"https://api.linkedin.com/v2/socialActions/{urn_id}/likes"
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            response = requests.get(social_actions_url, headers=headers)
            if response.status_code == 200:
                social_actions_data = response.json()
                
                return social_actions_data['paging']['total']
            
def linked_in_analytics_comments(token,id):
        urn_id = id
        social_actions_url = f"https://api.linkedin.com/v2/socialActions/{urn_id}/comments"
        headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
        }
        response = requests.get(social_actions_url, headers=headers)
        if response.status_code == 200:
            social_actions_data = response.json()
                
            return social_actions_data['paging']['total']