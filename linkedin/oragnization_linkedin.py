from collections import defaultdict
import requests
from datetime import datetime, <PERSON><PERSON><PERSON>


def get_linkedin_organization_id(access_token):
    url = "https://api.linkedin.com/v2/organizationAcls?q=roleAssignee"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "X-Restli-Protocol-Version": "2.0.0"
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        organizations = [
            item["organization"] for item in data.get("elements", [])
        ]
        return True,organizations
    else:
        return False,[]
    


def get_lifetime_organization_page_statistics(access_token, organization_id):
    url = f"https://api.linkedin.com/rest/organizationPageStatistics?q=organization&organization=urn:li:organization:{organization_id}"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "LinkedIn-Version": "202502",  # Required API version
        "Content-Type": "application/json"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Failed to retrieve data. Status code: {response.status_code}", "details": response.text}


def linked_in_follower_count(token, organization_urn):
    network_size_url = f"https://api.linkedin.com/v2/networkSizes/{organization_urn}?edgeType=CompanyFollowedByMember"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    response = requests.get(network_size_url, headers=headers)
    if response.status_code == 200:
        network_data = response.json()
        return network_data.get('firstDegreeSize', 0)  
    return 0

def get_linkedin_follower_statistics(org_urn, access_token):

    url = f"https://api.linkedin.com/rest/organizationalEntityFollowerStatistics?q=organizationalEntity&organizationalEntity={org_urn}"
    
    total_followers = linked_in_follower_count(access_token,org_urn)
    headers = {
        "Authorization": f"Bearer {access_token}",
        "LinkedIn-Version": "202503", 
        "Content-Type": "application/json"
    }
    
    data = {}
    response = requests.get(url, headers=headers)
    print("response.status_code",response.status_code)
    if response.status_code == 200:
        data['data'] = response.json()
        data['total'] = total_followers
        return True,data
    else:
        return False,{}




def get_linkedin_follower_stats(org_urn, start_date, end_date, access_token):
    """
    Fetch LinkedIn organizational entity follower statistics.

    :param org_urn: LinkedIn Organization URN (e.g., "urn:li:organization:2414183")
    :param start_date: Start date in "dd-mm-yyyy" format
    :param end_date: End date in "dd-mm-yyyy" format
    :param access_token: LinkedIn API access token
    :return: JSON response with follower statistics
    """
    

    # Convert dates to milliseconds (Unix timestamp in ms)
    start_timestamp = int(datetime.strptime(start_date, "%d-%m-%Y").timestamp() * 1000)
    end_timestamp = int(datetime.strptime(end_date, "%d-%m-%Y").timestamp() * 1000)


    total_followers = linked_in_follower_count(access_token,org_urn)
    

    url = "https://api.linkedin.com/rest/organizationalEntityFollowerStatistics"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "LinkedIn-Version": "202503"
    }
    
    params = {
        "q": "organizationalEntity",
        "organizationalEntity": org_urn,
        "timeIntervals.timeGranularityType": "DAY",
        "timeIntervals.timeRange.start": start_timestamp,
        "timeIntervals.timeRange.end": end_timestamp
    }
    
    response = requests.get(url, headers=headers, params=params)
    data = {'total_followers':total_followers}
    if response.status_code == 200:
        data['data'] = response.json()
        return True,data
    else:
        return False , {}


def linkedin_total_post(token, organization_urn):
    url = f"https://api.linkedin.com/v2/shares?q=owners&owners={organization_urn}"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(url, headers=headers)

    
    if response.status_code == 200:
        data = response.json()
        print("data",data)



def get_linkedin_impressions(token, organization_urn):
    url = "https://api.linkedin.com/v2/organizationalEntityShareStatistics"
    headers = {
        "Authorization": f"Bearer {token}",
        "X-Restli-Protocol-Version": "2.0.0"
    }
    params = {
        "q": "organizationalEntity",
        "organizationalEntity": organization_urn
    }
    
    response = requests.get(url, headers=headers, params=params)
    print(response.json())



def get_linkedin_share_statistics(organization_id, start_date=None, end_date=None, access_token="", version="202503"):

    start_timestamp = int(datetime.strptime(start_date, "%d-%m-%Y").timestamp() * 1000)
    end_timestamp = int(datetime.strptime(end_date, "%d-%m-%Y").timestamp() * 1000)

    url = "https://api.linkedin.com/rest/organizationalEntityShareStatistics"

    headers = {
        "X-Restli-Protocol-Version": "2.0.0",
        "Authorization": f"Bearer {access_token}",
        "LinkedIn-Version": version
    }

    time_intervals = f"(timeRange:(start:{start_timestamp},end:{end_timestamp}),timeGranularityType:DAY)"

    full_url = (
        f"{url}?q=organizationalEntity"
        f"&organizationalEntity=urn%3Ali%3Aorganization%3A{organization_id}"
        f"&timeIntervals={time_intervals}"
    )

    response = requests.get(full_url, headers=headers)
    data = {}
    if response.status_code == 200:
        data['data'] = response.json()
        return True,data
    else:
        return False,{}




def linkedin_total_posts_count(token, organization_urn, start_date, end_date):
    url = f"https://api.linkedin.com/v2/shares?q=owners&owners={organization_urn}"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        data = response.json()
        
        # Convert start and end dates to timestamps (milliseconds)
        start_timestamp = int(datetime.strptime(start_date, "%d-%m-%Y").timestamp() * 1000)
        end_timestamp = int((datetime.strptime(end_date, "%d-%m-%Y") + timedelta(days=1)).timestamp() * 1000)

        # Dictionary to store post counts per date
        post_counts = defaultdict(int)

        # Filter posts by created time
        for post in data.get("elements"):
            post_time = post["created"]["time"]
            if start_timestamp <= post_time <= end_timestamp:
                post_date = datetime.utcfromtimestamp(post_time / 1000).strftime('%d-%m-%Y')
                post_counts[post_date] += 1

        result = {
            "graph_data":[]
        }
        # Convert dictionary to list format
        result["graph_data"].append({"date": date, "count": count} for date, count in sorted(post_counts.items()))

        # Add total count at the end
        total_count = sum(post_counts.values())
        # print(data)
        result['total']=data['paging']['total']
        # return response.json()
        return True,result

    else:
        return False,[]

access_token = "AQVJKUwn_LpihYdcOE2BNVRD4j-hdZXjisw6xFaowL2IW9nSYS5ppDpPvbcOpKBbH7DRXDbxkNxYL0JzRyZ9IxzvyUB_OK1dGynIxVqrQZFQmA68eCsM0zsf8y48tMw4yB6_ImE2qb0VJuDtBN6JvMTqpZL7Ph0X9UHJNkCLam8VE-pJ9Eae62Fx8-nYfHwIf1UTZowUrQWosPDFHrCkzTKLcPxN1y0_85dZt9wnoBZoaA57FTrAJHlgKrx4GiPeTCCD5R1UrZGQuKNYrYZk8RU9VUdT1HwIAepHVVqW46k-PlcwjIORVI-ksS0B6kv3F2-cxWixCU8pNcb2nqBSGN_1fsfCOQ"
org_urn = "urn:li:organization:*********"
start_date = "01-03-2025"
end_date = "25-03-2025"

# # follower_stats = get_linkedin_follower_stats(org_urn, start_date, end_date, access_token)
# follower_stats = get_linkedin_impressions(access_token,org_urn)
# print(follower_stats)



def get_linkedin_date_impression(organization_id, start_date=None, end_date=None, access_token="", version="202503"):
    start_timestamp = int(datetime.strptime(start_date, "%d-%m-%Y").timestamp() * 1000)
    end_timestamp = int (datetime.strptime(end_date, "%d-%m-%Y").timestamp() * 1000)

    url = "https://api.linkedin.com/rest/organizationalEntityShareStatistics"

    headers = {
        "X-Restli-Protocol-Version": "2.0.0",
        "Authorization": f"Bearer {access_token}",
        "LinkedIn-Version": version
    }

    # Correct URL encoding for timeIntervals (Restli 2.0 format)
    time_intervals = f"(timeRange:(start:{start_timestamp},end:{end_timestamp}),timeGranularityType:DAY)"

    full_url = (
        f"{url}?q=organizationalEntity"
        f"&organizationalEntity=urn%3Ali%3Aorganization%3A{organization_id}"
        f"&timeIntervals={time_intervals}"
    )

    data = {}
    response = requests.get(full_url, headers=headers)

    if response.status_code == 200:
        data['data'] = response.json()
        return True,data
    else:
        return False,{}
    


def get_country_by_id(access_token,id):
    url = f"https://api.linkedin.com/v2/geo/{id}"

    payload = {}

    headers = {
    'accept': 'application/json',
    'version': '202503',
    'Authorization': f'Bearer {access_token}',
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    
    if response.status_code == 200:
        return True,response.json().get("defaultLocalizedName").get("value")
    else:
        return False,''
    

def get_seniority_by_id(access_token,id):
    url = f"https://api.linkedin.com/v2/seniorities/{id}?locale.language=en&locale.country=US"

    payload = {}

    headers = {
    'accept': 'application/json',
    'version': '202503',
    'Authorization': f'Bearer {access_token}',
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    
    if response.status_code == 200:
        return True,response.json().get("name").get("localized").get("en_US")
    else:
        return False,''

def get_industry_by_id(access_token,id):
    url = f"https://api.linkedin.com/v2/industryTaxonomyVersions/DEFAULT/industries/{id}?locale.language=en&locale.country=US"

    payload = {}

    headers = {
    'accept': 'application/json',
    'version': '202503',
    'Authorization': f'Bearer {access_token}',
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    
    if response.status_code == 200:
        return True,response.json().get("name").get("localized").get("en_US")
    else:
        return False,''

def get_function_by_id(access_token,id):
    url = f"https://api.linkedin.com/v2/functions/{id}?locale=en_US"

    payload = {}

    headers = {
    'accept': 'application/json',
    'version': '202503',
    'Authorization': f'Bearer {access_token}',
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    
    if response.status_code == 200:
        return True,response.json().get("name").get("localized").get("en_US")
    else:
        return False,''


# organization_id = "*********"
# access_token = "AQVJKUwn_LpihYdcOE2BNVRD4j-hdZXjisw6xFaowL2IW9nSYS5ppDpPvbcOpKBbH7DRXDbxkNxYL0JzRyZ9IxzvyUB_OK1dGynIxVqrQZFQmA68eCsM0zsf8y48tMw4yB6_ImE2qb0VJuDtBN6JvMTqpZL7Ph0X9UHJNkCLam8VE-pJ9Eae62Fx8-nYfHwIf1UTZowUrQWosPDFHrCkzTKLcPxN1y0_85dZt9wnoBZoaA57FTrAJHlgKrx4GiPeTCCD5R1UrZGQuKNYrYZk8RU9VUdT1HwIAepHVVqW46k-PlcwjIORVI-ksS0B6kv3F2-cxWixCU8pNcb2nqBSGN_1fsfCOQ"


# start_date = "01-03-2025" 
# end_date = "25-03-2025"    

# data = get_linkedin_share_statistics(organization_id,start_date,end_date,access_token)

# print(data)
