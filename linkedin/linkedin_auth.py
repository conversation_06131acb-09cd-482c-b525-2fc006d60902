import requests

# CLIENT_ID = '77onna49laarof'
# CLIENT_SECRET = 'BAv6ZjQeLlZY33Rq'
CLIENT_ID = '77cz59vd4rr7h9'
CLIENT_SECRET = 'WPL_AP1.sbr978ZUnHRw5wiO.9+UXhA=='
# REDIRECT_URI = 'http://127.0.0.1:8001/api/linkedin/'
# REDIRECT_URI = 'https://staging.flowkar.com/api/linkedin/'
# REDIRECT_URI = 'https://dev.flowkar.com/api/linkedin/'
REDIRECT_URI = 'https://api.flowkar.com/api/linkedin/'

from linkedin_api.clients.auth.client import AuthClient

def generate_url(user_id):

    auth_client = AuthClient(client_id=CLIENT_ID, client_secret=CLIENT_SECRET, redirect_url=REDIRECT_URI)

    return auth_client.generate_member_auth_url(scopes=['openid profile email','w_organization_social', 'r_basicprofile','w_member_social','r_organization_social','r_organization_admin','rw_organization_admin'],state=user_id)

def code_to_token_lin(code):
    url = 'https://www.linkedin.com/oauth/v2/accessToken'
    body = {
        'grant_type':'authorization_code',
        'code':code,
        'client_id':CLIENT_ID,
        'client_secret':CLIENT_SECRET,
        'redirect_uri':REDIRECT_URI
    }
    request = requests.post(url,body)
    data = request.json()
    return data['access_token']


def get_profile(token):
    url = 'https://api.linkedin.com/v2/userinfo'
    headers = {
        'Authorization':f'Bearer {token}'
    }
    request = requests.get(url,headers=headers)
    data = request.json()
    final_token = data['sub']
    return final_token

def get_profile_sharing(token):
    url = 'https://api.linkedin.com/v2/userinfo'
    headers = {
        'Authorization':f'Bearer {token}'
    }
    request = requests.get(url,headers=headers)
    data = request.json()
    final_token = data['sub']
    return final_token 

