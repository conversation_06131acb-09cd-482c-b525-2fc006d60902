from openai import OpenAI
from dotenv import load_dotenv
import os
from typing import Union, Optional

load_dotenv()
API_KEY = os.getenv("OPEN_AI_KEY")
client = OpenAI(api_key=API_KEY)


def generate_social_comment(post_text: str) -> str:
    """
    Generate a 10-word comment with emojis for social media posts.
    
    Args:
        post_text (str): The text content of the social media post
        
    Returns:
        str: A 10-word comment with relevant emojis
    """
    try:
        prompt = f"""
        Based on this social media post: "{post_text}"
        
        Generate a fun, engaging 10-word comment with 2-3 relevant emojis. 
        The comment should be:
        - Exactly 10 words
        - Positive and engaging
        - Include appropriate emojis
        - Relevant to the post content
        - Suitable for social media interaction
        
        Return only the comment with emojis, nothing else.
        """
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a social media expert who creates engaging, short comments with emojis."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=50,
            temperature=0.7
        )
        
        comment = response.choices[0].message.content.strip()
        
        # Ensure the comment is exactly 10 words
        words = comment.split()
        if len(words) > 10:
            comment = ' '.join(words[:10])
        elif len(words) < 10:
            # If less than 10 words, add some filler words
            filler_words = ["amazing", "love", "great", "awesome", "fantastic"]
            while len(words) < 10:
                words.append(filler_words[len(words) % len(filler_words)])
            comment = ' '.join(words)
        
        return comment
        
    except Exception as e:
        # Fallback comment if API fails
        fallback_comments = [
            "This is absolutely amazing! 🔥✨ Love it!",
            "Incredible content right here! 👏💯 Amazing work!",
            "This is so inspiring! 🌟💪 Keep it up!",
            "Absolutely love this! ❤️🔥 You're killing it!",
            "This is fantastic! 🎉✨ Great job here!"
        ]
        import random
        return random.choice(fallback_comments)


def generate_message_reply(message_text: str, sender_name: str = "User") -> str:
    """
    Generate a conversational reply to messages in social media app.
    
    Args:
        message_text (str): The message content to reply to
        sender_name (str): Name of the person who sent the message (optional)
        
    Returns:
        str: A conversational reply with emojis
    """
    try:
        prompt = f"""
        Someone named "{sender_name}" sent this message: "{message_text}"
        
        Generate a friendly, conversational reply that:
        - Is natural and engaging
        - Shows interest in their message
        - Includes 1-2 relevant emojis
        - Is appropriate for a social media chat
        - Keeps the conversation flowing
        - Is between 5-15 words
        
        Return only the reply, nothing else.
        """
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a friendly social media user who engages in natural conversations with emojis."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=60,
            temperature=0.8
        )
        
        reply = response.choices[0].message.content.strip()
        
        # Ensure the reply has a reasonable length
        words = reply.split()
        if len(words) > 15:
            reply = ' '.join(words[:15])
        elif len(words) < 3:
            # If too short, add some conversational words
            short_replies = [
                "That's interesting! 🤔",
                "Tell me more! 👀",
                "I see what you mean! 👍",
                "That's cool! 😊",
                "Interesting point! 💭"
            ]
            import random
            reply = random.choice(short_replies)
        
        return reply
        
    except Exception as e:
        # Fallback replies if API fails
        fallback_replies = [
            "That's really interesting! 🤔",
            "Tell me more about that! 👀",
            "I totally get what you mean! 👍",
            "That's so cool! 😊",
            "Interesting perspective! 💭",
            "Thanks for sharing! 🙏",
            "That sounds amazing! ✨",
            "I love that! ❤️",
            "That's awesome! 🎉",
            "Great point! 👏"
        ]
        import random
        return random.choice(fallback_replies)


def generate_quick_reply(message_text: str, reply_type: str = "friendly") -> str:
    """
    Generate quick, contextual replies based on message type.
    
    Args:
        message_text (str): The message content
        reply_type (str): Type of reply - "friendly", "supportive", "excited", "thoughtful"
        
    Returns:
        str: A quick reply with emojis
    """
    try:
        type_prompts = {
            "friendly": "Generate a friendly, casual reply",
            "supportive": "Generate a supportive, encouraging reply",
            "excited": "Generate an excited, enthusiastic reply",
            "thoughtful": "Generate a thoughtful, reflective reply"
        }
        
        prompt = f"""
        Message: "{message_text}"
        
        {type_prompts.get(reply_type, "Generate a friendly reply")} that:
        - Is 3-8 words long
        - Includes 1-2 relevant emojis
        - Matches the {reply_type} tone
        - Is natural and conversational
        
        Return only the reply.
        """
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": f"You are a social media user who gives {reply_type} replies."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=30,
            temperature=0.7
        )
        
        return response.choices[0].message.content.strip()
        
    except Exception as e:
        # Fallback quick replies by type
        fallback_replies = {
            "friendly": ["That's cool! 😊", "Nice! 👍", "Awesome! ✨"],
            "supportive": ["You got this! 💪", "Stay strong! 🌟", "I believe in you! 🙏"],
            "excited": ["That's amazing! 🎉", "So excited! 🔥", "Incredible! ⭐"],
            "thoughtful": ["Interesting! 🤔", "Good point! 💭", "Well said! 👏"]
        }
        
        import random
        return random.choice(fallback_replies.get(reply_type, fallback_replies["friendly"]))

