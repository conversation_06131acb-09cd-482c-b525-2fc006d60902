from openai import OpenAI
from dotenv import load_dotenv
import os
import base64
from typing import Union, Optional
import cv2
import tempfile

load_dotenv()
API_KEY = os.getenv("OPEN_AI_KEY")
client = OpenAI(api_key=API_KEY)

def generate_image_content(image_path: str, additional_context: Optional[str] = None) -> dict:
    """
    Generate 65-word social media content based on image analysis.

    Args:
        image_path (str): Path to the image file
        additional_context (str, optional): Additional context or requirements

    Returns:
        dict: Dictionary containing 'title' and 'content' based on image
    """
    system_prompt = """You are a professional social media content creator and strategist. Your task is to create engaging social media content based on visual content.
    
    When creating content, you should:
    1. Write exactly 65 words of high-quality social media content
    2. Make the content shareable, relatable, and engaging
    3. Include relevant hashtags or social media elements when appropriate
    4. Create content that encourages engagement (likes, comments, shares)
    5. Use conversational and relatable language
    6. Create a catchy title (5-8 words) for the content
    
    Guidelines for social media content:
    - Create content that encourages engagement (likes, comments, shares)
    - Act as a human don't include i analyed or anything you do to generate response just povide the response
    - Use conversational and relatable language
    - Make it relevant to current social media trends
    - Include call-to-actions when appropriate
    - Ensure exactly 65 words
    - Make content that fits various social media platforms (Instagram, Facebook, Twitter, LinkedIn)
    - Focus on storytelling and emotional connection
    - Write as if you're a real person sharing content, not an AI
    - Never mention "this image shows", "this frame captures", "I analyzed", or similar technical phrases
    - Write naturally as if you're a social media influencer or content creator
    
    Response format:
    Return your response in this exact format:
    TITLE: [Your catchy title here]
    CONTENT: [Your 65-word content here]"""

    try:
        # Encode image to base64
        with open(image_path, "rb") as image_file:
            image_data = base64.b64encode(image_file.read()).decode('utf-8')

        user_prompt = f"""Create exactly 65 words of engaging social media content that would perform well on platforms like Instagram, Facebook, Twitter, or LinkedIn.
        
        {f'Additional context: {additional_context}' if additional_context else ''}
        
        Please create social media content that:
        - Is engaging and shareable
        - Encourages audience interaction
        - Exactly 65 words
        - Professional yet relatable
        - Would work well across different social media platforms
        - Sounds natural and human-written
        - Does not mention technical analysis or AI processing
        
        Also create a catchy title (5-8 words) for the content."""

        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": user_prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=200,
            temperature=0.7
        )
        
        # Parse the response to extract title and content
        response_text = response.choices[0].message.content.strip()
        
        try:
            # Split the response by lines and extract title and content
            lines = response_text.split('\n')
            title = ""
            content = ""
            
            for line in lines:
                line = line.strip()
                if line.startswith('TITLE:'):
                    title = line.replace('TITLE:', '').strip()
                elif line.startswith('CONTENT:'):
                    content = line.replace('CONTENT:', '').strip()
            
            # If parsing fails, use the entire response as content
            if not title or not content:
                title = "Social Media Post"
                content = response_text
            
            return {
                "title": title,
                "content": content
            }
        except:
            # Fallback if parsing fails
            return {
                "title": "Social Media Post",
                "content": response_text
            }
    except Exception as e:
        return {
            "title": "Error",
            "content": f"Error generating social media content: {str(e)}"
        }


def generate_video_content(video_path: str, additional_context: Optional[str] = None) -> dict:
    """
    Generate 65-word social media content based on video analysis.
    Extracts a frame from the video and analyzes it as an image.

    Args:
        video_path (str): Path to the video file
        additional_context (str, optional): Additional context or requirements

    Returns:
        dict: Dictionary containing 'title' and 'content' based on video
    """
    system_prompt = """You are a professional social media content creator and strategist. Your task is to create engaging social media content based on visual content.
    
    When creating content, you should:
    1. Write exactly 65 words of high-quality social media content
    2. Make the content shareable, relatable, and engaging
    3. Include relevant hashtags or social media elements when appropriate
    4. Create content that encourages engagement (likes, comments, shares)
    5. Use conversational and relatable language
    6. Create a catchy title (5-8 words) for the content
    
    Guidelines for social media content:
    - Create content that encourages engagement (likes, comments, shares)
    - Act as a human don't include i analyed or anything you do to generate response just povide the response
    - Use conversational and relatable language
    - Make it relevant to current social media trends
    - Include call-to-actions when appropriate
    - Ensure exactly 65 words
    - Make content that fits various social media platforms (Instagram, Facebook, Twitter, LinkedIn, TikTok, YouTube)
    - Focus on storytelling and emotional connection
    - Write as if you're a real person sharing content, not an AI
    - Never mention "this video frame captures", "this image shows", "I analyzed", or similar technical phrases
    - Write naturally as if you're a social media influencer or content creator
    - Consider video-specific elements like motion, sound, and narrative flow when appropriate
    
    Response format:
    Return your response in this exact format:
    TITLE: [Your catchy title here]
    CONTENT: [Your 65-word content here]"""

    try:
        # Open the video file
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return {
                "title": "Error",
                "content": "Error: Could not open video file"
            }
        
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames == 0:
            cap.release()
            return {
                "title": "Error",
                "content": "Error: Video file appears to be empty or corrupted"
            }
        
        # Extract a frame from the middle of the video for better representation
        middle_frame = total_frames // 2
        cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
        
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            return {
                "title": "Error",
                "content": "Error: Could not extract frame from video"
            }
        
        # Save the frame as a temporary image file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
            cv2.imwrite(temp_file.name, frame)
            temp_image_path = temp_file.name
        
        try:
            # Encode the extracted frame to base64
            with open(temp_image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')

            user_prompt = f"""Create exactly 65 words of engaging social media content that would perform well on platforms like Instagram, Facebook, Twitter, LinkedIn, TikTok, or YouTube.
            
            {f'Additional context: {additional_context}' if additional_context else ''}
            
            Please create social media content that:
            - Is engaging and shareable
            - Encourages audience interaction
            - Exactly 65 words
            - Professional yet relatable
            - Would work well across different social media platforms
            - Sounds natural and human-written
            - Does not mention technical analysis or AI processing
            - Considers video-specific elements when appropriate
            
            Also create a catchy title (5-8 words) for the content."""

            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": user_prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            # Parse the response to extract title and content
            response_text = response.choices[0].message.content.strip()
            
            try:
                # Split the response by lines and extract title and content
                lines = response_text.split('\n')
                title = ""
                content = ""
                
                for line in lines:
                    line = line.strip()
                    if line.startswith('TITLE:'):
                        title = line.replace('TITLE:', '').strip()
                    elif line.startswith('CONTENT:'):
                        content = line.replace('CONTENT:', '').strip()
                
                # If parsing fails, use the entire response as content
                if not title or not content:
                    title = "Social Media Post"
                    content = response_text
                
                return {
                    "title": title,
                    "content": content
                }
            except:
                # Fallback if parsing fails
                return {
                    "title": "Social Media Post",
                    "content": response_text
                }
        
        finally:
            # Clean up the temporary file
            if os.path.exists(temp_image_path):
                os.unlink(temp_image_path)
                
    except Exception as e:
        return {
            "title": "Error",
            "content": f"Error generating social media content: {str(e)}"
        }


def generate_media_content(media_path: str, media_type: str = "auto", additional_context: Optional[str] = None) -> dict:
    """
    Generate 65-word social media content based on media analysis (image or video).

    Args:
        media_path (str): Path to the media file (image or video)
        media_type (str): Type of media - "image", "video", or "auto" (auto-detect)
        additional_context (str, optional): Additional context or requirements

    Returns:
        dict: Dictionary containing 'title' and 'content' based on media
    """
    # Auto-detect media type based on file extension
    if media_type == "auto":
        file_extension = os.path.splitext(media_path)[1].lower()
        if file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            media_type = "image"
        elif file_extension in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']:
            media_type = "video"
        else:
            return {
                "title": "Error",
                "content": f"Error: Unsupported file type {file_extension}. Supported formats: images (.jpg, .jpeg, .png, .gif, .bmp, .webp) and videos (.mp4, .avi, .mov, .wmv, .flv, .webm, .mkv)"
            }
    
    if media_type == "image":
        return generate_image_content(media_path, additional_context)
    elif media_type == "video":
        return generate_video_content(media_path, additional_context)
    else:
        return {
            "title": "Error",
            "content": f"Error: Invalid media type '{media_type}'. Use 'image', 'video', or 'auto'."
        }

