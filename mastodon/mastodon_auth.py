import requests
import secrets
import urllib.parse
from django.conf import settings

# Mastodon OAuth Configuration
MASTODON_CLIENT_NAME = 'Flowkar'
MASTODON_REDIRECT_URI = 'https://api.flowkar.com/api/mastodon/'
MASTODON_SCOPES = 'read write follow'
MASTODON_WEBSITE = 'https://flowkar.com'

def register_mastodon_app(instance_url):
    try:

        if not instance_url.startswith(('http://', 'https://')):
            instance_url = f'https://{instance_url}'
        
        instance_url = instance_url.rstrip('/')
        
        url = f"{instance_url}/api/v1/apps"
        data = {
            'client_name': MASTODON_CLIENT_NAME,
            'redirect_uris': MASTODON_REDIRECT_URI,
            'scopes': MASTODON_SCOPES,
            'website': MASTODON_WEBSITE
        }
        
        response = requests.post(url, data=data, timeout=10)
        response.raise_for_status()
        
        app_data = response.json()
        return {
            'client_id': app_data['client_id'],
            'client_secret': app_data['client_secret'],
            'instance_url': instance_url
        }
    except Exception as e:
        return None

def generate_mastodon_auth_url(instance_url, client_id, state):
    try:

        if not instance_url.startswith(('http://', 'https://')):
            instance_url = f'https://{instance_url}'
        
        # Remove trailing slash
        instance_url = instance_url.rstrip('/')
        
        params = {
            'client_id': client_id,
            'redirect_uri': MASTODON_REDIRECT_URI,
            'response_type': 'code',
            'scope': MASTODON_SCOPES,
            'state': state
        }
        
        auth_url = f"{instance_url}/oauth/authorize?" + urllib.parse.urlencode(params)
        return auth_url
    except Exception as e:
        return None

def exchange_code_for_token(instance_url, client_id, client_secret, code):
    try:
        if not instance_url.startswith(('http://', 'https://')):
            instance_url = f'https://{instance_url}'
        
        # Remove trailing slash
        instance_url = instance_url.rstrip('/')
        
        url = f"{instance_url}/oauth/token"
        data = {
            'client_id': client_id,
            'client_secret': client_secret,
            'redirect_uri': MASTODON_REDIRECT_URI,
            'grant_type': 'authorization_code',
            'code': code
        }
        
        response = requests.post(url, data=data, timeout=10)
        response.raise_for_status()
        
        token_data = response.json()
        return token_data['access_token']
    except Exception as e:
        print(f"Error exchanging code for token: {e}")
        return None

def get_mastodon_user_info(instance_url, access_token):
    try:
        if not instance_url.startswith(('http://', 'https://')):
            instance_url = f'https://{instance_url}'
        
        instance_url = instance_url.rstrip('/')
        
        url = f"{instance_url}/api/v1/accounts/verify_credentials"
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        user_data = response.json()
        return {
            'id': user_data['id'],
            'username': user_data['username'],
            'display_name': user_data['display_name'],
            'avatar': user_data['avatar'],
            'url': user_data['url'],
            'followers_count': user_data['followers_count'],
            'following_count': user_data['following_count'],
            'statuses_count': user_data['statuses_count']
        }
    except Exception as e:
        return None