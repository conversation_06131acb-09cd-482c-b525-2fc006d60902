import requests
import time

def wait_until_video_ready(instance_url, access_token, media_id, timeout=60):
    """
    Check every 2 seconds if video processing is complete
    """
    headers = {'Authorization': f'Bearer {access_token}'}
    url = f"{instance_url}/api/v1/media/{media_id}"

    for _ in range(timeout // 2):
        res = requests.get(url, headers=headers)
        if res.status_code == 200:
            data = res.json()
            if data.get("url"):  # Media is processed
                return True
        time.sleep(2)
    return False

def post_mastodon_video_status(instance_url, access_token, status_text, video_paths):
    """
    Upload multiple videos and post a Mastodon status
    """
    try:
        # Normalize instance URL
        if not instance_url.startswith(('http://', 'https://')):
            instance_url = f'https://{instance_url}'
        instance_url = instance_url.rstrip('/')

        headers = {'Authorization': f'Bearer {access_token}'}
        media_ids = []

        # Step 1: Upload all videos
        for video_path in video_paths:
            with open(video_path, 'rb') as file:
                files = {'file': file}
                upload_url = f"{instance_url}/api/v2/media"
                res = requests.post(upload_url, headers=headers, files=files)
                res.raise_for_status()
                media_data = res.json()
                media_id = media_data['id']
                media_ids.append(media_id)

                print(f"Uploaded {video_path} with media ID: {media_id}")

        # Step 2: Wait for all videos to process
        all_processed = True
        for media_id in media_ids:
            if not wait_until_video_ready(instance_url, access_token, media_id):
                print(f"Video {media_id} not processed in time")
                all_processed = False
                break
            print(f"Video {media_id} processed successfully")

        if not all_processed:
            return {'status': False, 'message': 'One or more videos not processed in time'}

        # Step 3: Post status with videos
        post_url = f"{instance_url}/api/v1/statuses"
        data = {
            'status': status_text,
            'media_ids[]': media_ids  # This is the correct way to send multiple media IDs
        }

        print(f"Posting status with media IDs: {media_ids}")
        post_res = requests.post(post_url, headers=headers, data=data)
        post_res.raise_for_status()


        return True,post_res.json().get('id')

    except Exception as e:
        return False,''

# # Example usage with multiple videos
# instance_url = "mastodon.social"
# access_token = "47lxDDj1xbE6cGlQcEwb1HpRSshir9C3SXR5OtmB7nM"
# status_text = "🔥 Posting multiple test videos to Mastodon!"
# video_paths = ["1.mov"]  

# result = post_mastodon_video_status(instance_url, access_token, status_text, video_paths)
# print(result)


