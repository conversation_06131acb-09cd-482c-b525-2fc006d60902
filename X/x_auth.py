from datetime import datetime, timedelta
from django.conf import settings
import base64
from django.shortcuts import redirect
import requests
import urllib.parse
import hashlib
import os
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from Authentication.jwt_auth import CustomJWTAuthentication
from Authentication.models import ThirdPartyAuth,SpecialSocial
from helpers.id_decode import decode_token

REDIRECT_URI = "https://api.flowkar.com/api/twitter-callback/"
# REDIRECT_URI = http://127.0.0.1:8000/api/twitter-callback/
# Twitter API Configuration
TWITTER_CONFIG = {
    "CLIENT_ID": "SW12aEFOUjFudHRBZkszcjVRSl86MTpjaQ",
    "CLIENT_SECRET": "fXknBr7dxmfCPtTroY3AqgwzCGKsNFDkJNujfQE15ZrP9Sgmt7",
    "REDIRECT_URI": REDIRECT_URI,
    "AUTH_URL": "https://twitter.com/i/oauth2/authorize",
    "TOKEN_URL": "https://api.twitter.com/2/oauth2/token",
    "USERINFO_URL": "https://api.twitter.com/2/users/me",
    "USERINFO_URL_V2": "https://api.twitter.com/2/users/me?user.fields=profile_image_url",
    "POST_TWEET_URL": "https://api.twitter.com/2/tweets",
    "SCOPES": "tweet.read users.read offline.access tweet.write media.write like.read dm.read dm.write"
}

def generate_pkce():
    """Generate PKCE code verifier and challenge for OAuth2."""
    code_verifier = base64.urlsafe_b64encode(os.urandom(32)).decode("utf-8").rstrip("=")
    code_challenge = base64.urlsafe_b64encode(hashlib.sha256(code_verifier.encode()).digest()).decode("utf-8").rstrip("=")
    return code_verifier, code_challenge

CODE_VERIFIER, CODE_CHALLENGE = generate_pkce()

class TwitterLoginView(APIView):
    """Handle Twitter OAuth2 login initiation."""
    authentication_classes = [CustomJWTAuthentication]
    def get(self, request):
        brand_id = request.headers.get('brand')
        token = request.headers.get('Authorization')
        user_id = decode_token(token)
        params = {
            "response_type": "code",
            "client_id": TWITTER_CONFIG["CLIENT_ID"],
            "redirect_uri": TWITTER_CONFIG["REDIRECT_URI"],
            "scope": TWITTER_CONFIG["SCOPES"],
            "state": brand_id,
            "code_challenge": CODE_CHALLENGE,
            "code_challenge_method": "S256",
        }
        try:
            check_user = SpecialSocial.objects.get(user_id=user_id)
            if check_user.is_active:
                auth_url = f"{TWITTER_CONFIG['AUTH_URL']}?{urllib.parse.urlencode(params)}"
                return Response({"status":True,'message':"Successfully created the auth url","url": auth_url}, status=status.HTTP_200_OK)
            else:
                return Response({"status":False,"message":"You are not subscribed to X"}, status=status.HTTP_200_OK)
        except SpecialSocial.DoesNotExist:
            return Response({"status":False,"message":"You are not subscribed to X"}, status=status.HTTP_200_OK)

class TwitterCallbackView(APIView):
    """Handle Twitter OAuth2 callback and token exchange."""
    
    def _get_client_credentials(self):
        client_credentials = f"{TWITTER_CONFIG['CLIENT_ID']}:{TWITTER_CONFIG['CLIENT_SECRET']}"
        credentials_b64 = base64.b64encode(client_credentials.encode()).decode()
        return credentials_b64

    def _exchange_code_for_tokens(self, code):
        """Exchange authorization code for access and refresh tokens."""
        data = {
            "client_id": TWITTER_CONFIG["CLIENT_ID"],
            "redirect_uri": TWITTER_CONFIG["REDIRECT_URI"],
            "code": code,
            "grant_type": "authorization_code",
            "code_verifier": CODE_VERIFIER,
        }
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {self._get_client_credentials()}"
        }
        response = requests.post(TWITTER_CONFIG["TOKEN_URL"], data=data, headers=headers)
        if response.status_code != 200:
            raise Exception(f"Token exchange failed: {response.text}")
        return response.json()

    def _get_user_info(self, access_token):
        """Fetch user information using access token."""
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(TWITTER_CONFIG["USERINFO_URL_V2"], headers=headers)
        if response.status_code != 200:
            raise Exception(f"Failed to fetch user info: {response.text}")
        return response.json()

    def get(self, request):
        """Handle OAuth2 callback and return user data with tokens."""
        try:
            code = request.GET.get("code")
            state = request.GET.get("state")
            if not code:
                return Response({"error": "Authorization code missing"}, status=status.HTTP_400_BAD_REQUEST)

            # Exchange code for tokens
            token_data = self._exchange_code_for_tokens(code)
            access_token = token_data.get("access_token")
            refresh_token = token_data.get("refresh_token")

            # Get user information
            user_data = self._get_user_info(access_token)
            profile_image_url = user_data.get("data", {}).get("profile_image_url", "")

            response_data = {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "user": user_data,
                "profile_image": profile_image_url
            }
            third_party_data = ThirdPartyAuth.objects.get(brand_id=state)
            third_party_data.x_access_token = access_token
            third_party_data.x_refresh_token = refresh_token
            third_party_data.x_check = True
            third_party_data.x_user_id = user_data.get("data", {}).get("id", "")
            third_party_data.save()
            return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{state}')

        except Exception as e:
           return redirect(f'https://api.flowkar.com/api/social-connect-deeplink/{state}')

def get_and_update_x_token(refresh_token, brand_id):
    """Get new access token using refresh token and update brand's X token."""
    try:
        if not refresh_token or not brand_id:
            return None, "Refresh token and brand_id are required"

        # Get client credentials
        client_credentials = f"{TWITTER_CONFIG['CLIENT_ID']}:{TWITTER_CONFIG['CLIENT_SECRET']}"
        credentials_b64 = base64.b64encode(client_credentials.encode()).decode()

        data = {
            "client_id": TWITTER_CONFIG["CLIENT_ID"],
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {credentials_b64}"
        }
        response = requests.post(TWITTER_CONFIG["TOKEN_URL"], data=data, headers=headers)

        if response.status_code != 200:
            return None, f"Failed to refresh access token: {response.text}"

        token_data = response.json()
        new_access_token = token_data.get("access_token")
        new_refresh_token = token_data.get("refresh_token")

        # Update brand's X token
        try:
            third_party_data = ThirdPartyAuth.objects.get(brand_id=brand_id)
            third_party_data.x_access_token = new_access_token
            if new_refresh_token:
                third_party_data.x_refresh_token = new_refresh_token
            third_party_data.save()
        except ThirdPartyAuth.DoesNotExist:
            return None, f"Brand with ID {brand_id} not found"

        return new_access_token

    except Exception as e:
        return None
    


def get_x_user_info(access_token):
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(TWITTER_CONFIG["USERINFO_URL_V2"], headers=headers)
        if response.status_code != 200:
            return False,{}
        
        profile_data = response.json()
        return  True,{
            "username": profile_data.get("data", {}).get("username", ""),
            "profile_picture_url": profile_data.get("data", {}).get("profile_image_url", ""),
            "name": profile_data.get("data", {}).get("name", ""),
        }




class TwitterRefreshTokenView(APIView):
    """Handle Twitter OAuth2 token refresh."""
    
    def _get_client_credentials(self):
        client_credentials = f"{TWITTER_CONFIG['CLIENT_ID']}:{TWITTER_CONFIG['CLIENT_SECRET']}"
        credentials_b64 = base64.b64encode(client_credentials.encode()).decode()
        return credentials_b64

    def post(self, request):
        """Refresh access token using refresh token."""
        try:
            refresh_token = request.data.get("refresh_token")
            if not refresh_token:
                return Response({"error": "Refresh token is required"}, status=status.HTTP_400_BAD_REQUEST)

            data = {
                "client_id": TWITTER_CONFIG["CLIENT_ID"],
                "grant_type": "refresh_token",
                "refresh_token": refresh_token,
            }

            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": f"Basic {self._get_client_credentials()}"
            }
            response = requests.post(TWITTER_CONFIG["TOKEN_URL"], data=data, headers=headers)

            if response.status_code != 200:
                return Response({"error": f"Failed to refresh access token: {response.text}"}, status=status.HTTP_400_BAD_REQUEST)

            token_data = response.json()
            new_access_token = token_data.get("access_token")
            new_refresh_token = token_data.get("refresh_token")

            response_data = {
                "access_token": new_access_token,
                "refresh_token": new_refresh_token or refresh_token
            }
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)