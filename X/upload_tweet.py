import os
import time
import requests

MEDIA_UPLOAD_URL = "https://api.x.com/2/media/upload"
POST_TWEET_URL = "https://api.x.com/2/tweets"

def post_tweet_with_asset(access_token, file_paths, tweet_text):
    """
    Uploads multiple media files and posts a tweet with all uploaded media.
    Args:
        file_paths: List of paths to media files
        tweet_text: Text content of the tweet
    Returns:
        tuple: (success: bool, tweet_id: str or None)
    """
    headers = {"Authorization": f"Bearer {access_token}"}
    media_ids = []
    
    # Upload all media files
    for file_path in file_paths:
        try:
            with open(file_path, "rb") as file:
                files = {"media": file,}
                data = {"media_category": "tweet_image"} 
                upload_response = requests.post(MEDIA_UPLOAD_URL, headers=headers, files=files, data=data)
            print(upload_response.json())
            if upload_response.status_code != 200:
                print(f"Media upload failed for {file_path} with status {upload_response.status_code}: {upload_response.text}")
                return False, None

            try:
                media_id = upload_response.json().get("data", {}).get("id")
                if not media_id:
                    print(f"Upload response received for {file_path}, but no media ID found:", upload_response.json())
                    return False, None
                media_ids.append(media_id)
                print(f"Successfully uploaded {file_path} with media ID: {media_id}")
            except requests.exceptions.JSONDecodeError:
                print(f"Error: Upload response for {file_path} is not in JSON format. Raw response:", upload_response.text)
                return False, None
        except FileNotFoundError:
            print(f"Error: File not found: {file_path}")
            return False, None

    if not media_ids:
        print("No media files were successfully uploaded")
        return False, None

    # Now post the tweet with all media
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    payload = {
        "text": tweet_text,
        "media": {"media_ids": media_ids}
    }

    tweet_response = requests.post(POST_TWEET_URL, headers=headers, json=payload)

    if tweet_response.status_code == 201:
        tweet_id = tweet_response.json().get("data", {}).get("id")
        if tweet_id:
            print(f"Tweet posted successfully! Tweet ID: {tweet_id}")
            return True, tweet_id
    
    print(f"Failed to post tweet: {tweet_response.status_code} - {tweet_response.text}")
    return False, None


def post_text_tweet(access_token, text):
    """Posts a tweet with the uploaded media."""
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    payload = {
        "text": text,
    }

    response = requests.post(POST_TWEET_URL, headers=headers, json=payload)

    if response.status_code == 201:
        tweet_id = response.json().get("data", {}).get("id")
        if tweet_id:
            return True, tweet_id
    else:
        return False, ''


#Video upload

def upload_video_and_post_tweet(token, file_paths, tweet_text):
    """
    Uploads multiple video files and posts a tweet with all uploaded media.
    Args:
        token: Authentication token
        file_paths: List of paths to video files
        tweet_text: Text content of the tweet
    Returns:
        tuple: (success: bool, tweet_id: str or None)
    """
    headers = {
        "Authorization": f"Bearer {token}"
    }
    media_ids = []

    for file_path in file_paths:
        # Step 1: INIT
        total_bytes = os.path.getsize(file_path)
        init_url = "https://api.x.com/2/media/upload"
        init_data = {
            "command": "INIT",
            "media_type": "video/mp4",
            "total_bytes": total_bytes,
            "media_category": "amplify_video"
        }

        init_response = requests.post(init_url, headers=headers, files=init_data)
        media_id = init_response.json()["data"]["id"]
        print(f"[INIT] Media ID: {media_id}")

        # Step 2: APPEND (upload file in chunks)
        segment_index = 0
        chunk_size = 4 * 1024 * 1024  # 4MB
        append_url = "https://api.x.com/2/media/upload"
        
        with open(file_path, "rb") as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                files = {
                    "command": (None, "APPEND"),
                    "media_id": (None, media_id),
                    "segment_index": (None, str(segment_index)),
                    "media": ("blob", chunk)
                }
                response = requests.post(append_url, headers=headers, files=files)
                if response.status_code >= 300:
                    raise Exception(f"[APPEND] Upload failed: {response.text}")
                print(f"[APPEND] Uploaded chunk {segment_index}")
                segment_index += 1

        # Step 3: FINALIZE
        finalize_data = {
            "command": "FINALIZE",
            "media_id": media_id
        }
        finalize_response = requests.post(init_url, headers=headers, files=finalize_data)
        finalize_json = finalize_response.json()
        print("[FINALIZE] Finalized upload.")

        # Step 4: STATUS polling (if required)
        if "processing_info" in finalize_json.get("data", {}):
            state = finalize_json["data"]["processing_info"]["state"]
            while state != "succeeded":
                check_after_secs = finalize_json["data"]["processing_info"].get("check_after_secs", 2)
                time.sleep(check_after_secs)
                status_url = f"https://api.x.com/2/media/upload?command=STATUS&media_id={media_id}"
                status_response = requests.get(status_url, headers=headers)
                status_json = status_response.json()
                state = status_json["data"]["processing_info"]["state"]
                print(f"[STATUS] Processing state: {state}")
                if state == "failed":
                    raise Exception("Media processing failed.")
        
        media_ids.append(media_id)

    # Step 5: Post tweet with all media
    tweet_url = "https://api.x.com/2/tweets"
    tweet_data = {
        "text": tweet_text,
        "media": {
            "media_ids": media_ids
        }
    }
    tweet_headers = headers.copy()
    tweet_headers["Content-Type"] = "application/json"
    tweet_response = requests.post(tweet_url, headers=tweet_headers, json=tweet_data)
    if tweet_response.status_code == 200 or tweet_response.status_code == 201:
        tweet_json = tweet_response.json()
        tweet_id = tweet_json["data"]["id"]
        return True, tweet_id
    else:
        print(f"Failed to post tweet: {tweet_response.status_code} - {tweet_response.text}")
        return False, ''


# file_paths = ["111.mp4","111.mp4"]  # Now accepts a list of file paths
# tweet_id = upload_video_and_post_tweet(token, file_paths, tweet_text="🚀 Posting video test!")

# print("Tweet ID:", tweet_id)


# Example usage
# file_paths = ["123.jpeg", "123.jpeg"]
# success, tweet_id = post_text_tweet("I Love Flowkar Its Just Wow")
# print(success, tweet_id)
# if not success:
#     print("Failed to post tweet with media.")