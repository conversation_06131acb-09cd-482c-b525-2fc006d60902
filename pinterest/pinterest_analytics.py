import requests
from datetime import datetime, timedelta

def get_analytics_data(start_date, end_date, access_token):
    today = datetime.today()

    if not end_date:
        end_date = today
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d')

    max_start_date = end_date - timedelta(days=90)

    if not start_date:
        start_date = max_start_date
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if start_date < max_start_date:
            print(f"Adjusting start_date from {start_date.strftime('%Y-%m-%d')} to {max_start_date.strftime('%Y-%m-%d')} (max 90 days allowed)")
            start_date = max_start_date

    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    }

    url = f'https://api.pinterest.com/v5/user_account/analytics?start_date={start_date_str}&end_date={end_date_str}'
    
    try:
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            
            result = {
                'IMPRESSION': data.get('all', {}).get('summary_metrics', {}).get('IMPRESSION', 0),
                'ENGAGEMENT': data.get('all', {}).get('summary_metrics', {}).get('ENGAGEMENT', 0),
                'PIN_CLICK': data.get('all', {}).get('summary_metrics', {}).get('PIN_CLICK', 0),
                'OUTBOUND_CLICK': data.get('all', {}).get('summary_metrics', {}).get('OUTBOUND_CLICK', 0),
                'SAVE': data.get('all', {}).get('summary_metrics', {}).get('SAVE', 0),
                'daily_data': []
            }
            
            for daily_entry in data.get('all', {}).get('daily_metrics', []):
                daily_result = {
                    'date': daily_entry.get('date'),
                    'IMPRESSION': daily_entry.get('metrics', {}).get('IMPRESSION', 0),
                    'ENGAGEMENT': daily_entry.get('metrics', {}).get('ENGAGEMENT', 0),
                    'PIN_CLICK': daily_entry.get('metrics', {}).get('PIN_CLICK', 0),
                    'OUTBOUND_CLICK': daily_entry.get('metrics', {}).get('OUTBOUND_CLICK', 0),
                    'SAVE': daily_entry.get('metrics', {}).get('SAVE', 0)
                }
                result['daily_data'].append(daily_result)
            
            return True, result
        else:
            return False, { }
    
    except Exception as e:
        return False, { }