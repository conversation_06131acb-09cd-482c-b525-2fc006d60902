import requests

def create_board(board_name,board_discription,access_token):
    url = "https://api-sandbox.pinterest.com/v5/boards/"

    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    payload = {
        "name": board_name,
        "description": board_discription
    }

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 201:
        print("Board created successfully!")
        board_id = response.json()['id']
        return f"Board ID: {board_id}"
    else:
        return "Error creating board:", response.json()
    
board_name = 'This Board Is Automatically Created By Yooii'
dis = '<PERSON><PERSON><PERSON>'


# Define the endpoint
def post_pin(access_token, image_url, title, description):
    try:
        url = "https://api-sandbox.pinterest.com/v5/pins/"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        board_headers = {
            'Authorization': f'Bearer {access_token}',
        }
        
        board_url = "https://api-sandbox.pinterest.com/v5/boards"
        board_response = requests.get(board_url,headers=board_headers)
        board_data = board_response.json()

        if board_data['items'] == []:
            board_id = create_board(board_name,dis,access_token).split()[-1]

        else:
            board_id = board_data['items'][0]['id']
        pin_ids = []
        payload = {
                "board_id": board_id,
                "media_source": {
                    "source_type": "image_url",
                    "url": image_url
                },
                "title": title,
                "description": description,
        }
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code == 201:
            print("Pin uploaded successfully!")
            pin_id = response.json()['id']
            pin_ids.append(pin_id)
            return True , pin_id
        else:
            print("Error uploading pin:", response.json())
            return False
    except Exception as e :
        print(e)
        return False

def upload_video_to_pinterest(access_token, video_path, title, description):
    media_url = "https://api-sandbox.pinterest.com/v5/media"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "media_type": "video"
    }

    response = requests.post(media_url, headers=headers, json=payload)
    if response.status_code != 200:
        print("Error getting upload URL:", response.json())

    upload_data = response.json()
    upload_url = upload_data.get("upload_url")
    media_id = upload_data.get("media_id")
    upload_parameters = upload_data.get("upload_parameters")

    # Step 2: Upload the Video to the Provided URL with the Parameters
    files = {'file': open(video_path, 'rb')}
    response = requests.post(upload_url, data=upload_parameters, files=files)
    if response.status_code != 204:
        print("Error uploading video:", response.text)
        return

    board_headers = {
        'Authorization': f'Bearer {access_token}',
    }
    
    board_url = "https://api-sandbox.pinterest.com/v5/boards"
    board_response = requests.get(board_url,headers=board_headers)
    board_data = board_response.json()

    if board_data['items'] == []:
        board_id = create_board(board_name,dis,access_token).split()[-1]

    else:
        board_id = board_data['items'][0]['id']
    # Step 3: Create a Pin Using the Uploaded Video
    pin_url = "https://api-sandbox.pinterest.com/v5/media"
    pin_payload = {
        "board_id": board_id,
        "title": title,
        "description": description,
        "media_source": {
            "media_id": media_id
        }
    }

    pin_response = requests.post(pin_url, headers=headers, json=pin_payload)
    if pin_response.status_code != 201:
        print("Error creating pin:", pin_response.json())
        return

    print("Pin created successfully:", pin_response.json())

# # Example usage
# access_token = "pina_AEAY7ZYWADUHCAYAGAAPCDQECWYLLEABACGSPSDKPKBPHIEQI77YSZWIMFTFA4ASV6XQEIFWFYOQBZ2FFVADMPTF2KAEBSIA"
# image_urls = [
    
# ]
# title = "Sample Title"
# description = "Sample Description"

# pin_ids = post_pin(access_token, image_urls, title, description)
# print("Uploaded Pin IDs:", pin_ids)