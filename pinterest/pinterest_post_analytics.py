import requests
from datetime import datetime, timedelta

def get_pinterest_post_analytics(pin_id, access_token, days=90):
    end_date = datetime.today().strftime('%Y-%m-%d')  
    start_date = (datetime.today() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    metric_types = "TOTAL_COMMENTS,TOTAL_REACTIONS"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    }
    
    url = f'https://api.pinterest.com/v5/pins/{pin_id}/analytics'
    params = {
        'pin_id': pin_id,
        'start_date': start_date,
        'end_date': end_date,
        'metric_types': metric_types
    }
    
    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        data = response.json()
        lifetime_metrics = data.get('all', {}).get('lifetime_metrics', {})
        
        return {'TOTAL_COMMENTS': lifetime_metrics.get('TOTAL_COMMENTS', 0),'TOTAL_REACTIONS': lifetime_metrics.get('TOTAL_REACTIONS', 0),}


    else:
        return {"error": response.status_code, "message": response.text}



# pin_id = "1076641854676744087"
# access_token = "pina_AMAY7ZYWADUHCAYAGAAPCDWNTGCO7FIBQBIQCTAVVPMQOX7ZBFBCGN4ORL33MWLP3XD7TDFRZRYNMSWVXFXPLAZ43KQUL5AA"
# analytics_data = get_pinterest_post_analytics(pin_id, access_token)
# print(analytics_data)