import requests
from urllib.parse import urlencode               
import base64

# Replace these with your own values
CLIENT_ID = '1501071'
CLIENT_SECRET = '95c26a83d14afa7dcac57af1ade42ea2c5c28fcf'
# REDIRECT_URI = 'https://staging.yooii.com/api/pinterest/'
# REDIRECT_URI = 'https://staging.flowkar.com/api/pinterest/'
# REDIRECT_URI = 'https://dev.flowkar.com/api/pinterest/'
REDIRECT_URI = 'https://api.flowkar.com/api/pinterest/'

def get_user_url_pinterest(state):

    auth_url = 'https://www.pinterest.com/oauth/'
    params = {
        'response_type': 'code',
        'client_id': CLIENT_ID,
        'redirect_uri': REDIRECT_URI,
        'scope': 'boards:read boards:read_secret boards:write boards:write_secret pins:read pins:read_secret pins:write pins:write_secret user_accounts:read catalogs:read catalogs:write',
        'state':state
    }
    auth_request_url = f"{auth_url}?{urlencode(params)}"

    return auth_request_url



# def pin_code_to_token(code):
#     encoded_credentials = base64.b64encode(f"{CLIENT_ID}:{CLIENT_SECRET}".encode()).decode()

#     token_url = "https://api.pinterest.com/v5/oauth/token"

#     headers = {
#         'Authorization': f'Basic {encoded_credentials}',
#         'Content-Type': 'application/x-www-form-urlencoded'
#     }

#     data = {
#         'grant_type': 'authorization_code',
#         'code': code,
#         'redirect_uri': REDIRECT_URI
#     }

#     response = requests.post(token_url, headers=headers, data=data)

#     token_data = response.json()

#     if response.status_code == 200:
#         return token_data['access_token']
#     else:
#         return  token_data

# access_token = 'aaaacb7662c34bf8fd2f45b97c7cad6d244ef9e0'

# print(pin_code_to_token(access_token))

def pin_code_to_token(code):
    encoded_credentials = base64.b64encode(f"{CLIENT_ID}:{CLIENT_SECRET}".encode()).decode()

    token_url = "https://api-sandbox.pinterest.com/v5/oauth/token"

    headers = {
        'Authorization': f'Basic {encoded_credentials}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    data = {
        'grant_type': 'authorization_code',
        'code': code,
        'redirect_uri': REDIRECT_URI
    }

    response = requests.post(token_url, headers=headers, data=data)

    token_data = response.json()

    if response.status_code == 200:
        return token_data['access_token']
    else:
        return token_data

# # Example usage with an authorization code (not access token)
# authorization_code = 'b3d458facafd110c4b246800355f1564bbae72a3'
# access_token = pin_code_to_token(authorization_code)
# print(access_token)