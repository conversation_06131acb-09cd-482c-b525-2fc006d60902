import requests
def get_pinterest_profile(token):

        if not token:
            return {'profile_url': '', 'username': '', 'name': '', 'profile_image': '', 'error': 'No token provided'}

        profile_url = 'https://api-sandbox.pinterest.com/v5/user_account' 
        headers = {'Authorization': f'Bearer {token}'}

        try:
            response = requests.get(profile_url, headers=headers)
            response.raise_for_status()

            profile_data = response.json()
        
            return {
                'profile_url': f"https://www.pinterest.com/{profile_data.get('username', '')}",
                'username': profile_data.get('username', ''),
                'name': profile_data.get('username', ''), 
                'profile_image': profile_data.get('profile_image', '')
            }
        except Exception as e:
            return {'profile_url': '', 'username': '', 'name': '', 'profile_image': '', 'error': f'Error: {e}'}
        

def get_pinterest_profile_for_multiple(token):

        if not token:
            return {'profile_url': '', 'username': '', 'name': '', 'profile_image': '', 'error': 'No token provided'}

        profile_url = 'https://api-sandbox.pinterest.com/v5/user_account' 
        headers = {'Authorization': f'Bearer {token}'}

        try:
            response = requests.get(profile_url, headers=headers)
            response.raise_for_status()

            profile_data = response.json()
            return profile_data.get('username', '')

        except Exception as e:
            return {'profile_url': '', 'username': '', 'name': '', 'profile_image': '', 'error': f'Error: {e}'}