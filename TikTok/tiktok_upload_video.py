import json
import time
import requests


def tiktok_upload_videos(access_token, videourl, description=None):
    url = 'https://open.tiktokapis.com/v2/post/publish/video/init/'

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {access_token}'
    }

    payload = json.dumps({
        "post_info": {
            "title": description,
            "privacy_level": "SELF_ONLY",
            "disable_duet": False,
            "disable_comment": False,
            "disable_stitch": False,
            "video_cover_timestamp_ms": 1000
        },
        "source_info": {
            "source": "PULL_FROM_URL",
            "video_url": videourl[0]
        }
    })

    print(payload)

    # time.sleep(5)

    post_request = requests.post(url=url, headers=headers, data=payload)
    post_response = post_request.json()
    print(post_response)
    if post_request.status_code == 200:
        publish_id = post_response['data']['publish_id']
        print(f'Publish Id {publish_id}',flush=True)
        upload_status_url = "https://open.tiktokapis.com/v2/post/publish/status/fetch/"
        # upload_status_body = {
        #     "publish_id": publish_id
        # }
        # post_upload_status = requests.post(
        #     url=upload_status_url, headers=headers, data=upload_status_body)
        # post_upload_status_response = post_upload_status.json()
        # if post_upload_status.status_code == 200:
        #     post_id = post_upload_status_response['data']['publicaly_available_post_id']

        #     return True, post_id
        # else:
        return True, publish_id

    else:
        return False, 0


# print(tiktok_upload_videos('act.UNbvgrGg8lT1PqUKKyXkeYuNOjpPRIKbKKdbEIWGJSlj9EsuzOs2BG0jwbEz!4411.va',
#       ['http://staging.yooii.com/media/post_files/compressed_compressed_VID_515780425_052949_122_emmRmhr.mp4'], 'New World Hello'))
