import requests


CLIENT_KEY = 'sbaw4ah8fb382yi9a0'
CLIENT_SECRET = 'xvrUqlzr8xYCpz4ycrJiUSLVumysLrBi'
# REDIRECT_URL = 'https://staging.flowkar.com/api/tiktok-auth/'
REDIRECT_URL = 'https://api.flowkar.com/api/tiktok-auth/'

def tiktok_auth_url(user_id):
    url = f"https://www.tiktok.com/v2/auth/authorize/?client_key={CLIENT_KEY}&response_type=code&scope=user.info.basic,video.publish,video.upload,video.list,user.info.stats&state={user_id}&redirect_uri={REDIRECT_URL}"
    return url

def tiktok_code_token(code):
    url = "https://open.tiktokapis.com/v2/oauth/token/"

    body = {
        "client_key":CLIENT_KEY,
        "client_secret":CLIENT_SECRET,
        "code":code,
        "grant_type":"authorization_code",
        "redirect_uri":REDIRECT_URL
    }

    post_request = requests.post(url,data=body)
    post_response = post_request.json()

    if post_request.status_code == 200:
        access_token = post_response['access_token']
        refresh_token = post_response['refresh_token']

        return access_token,refresh_token
    else:
        return None,None