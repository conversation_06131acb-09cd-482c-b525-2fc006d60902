import pytumblr

CONSUMER_KEY = '23pJ5t0JKNSORTvsY2e9HyQF002wMpYlk01mce4V7C0yLkHFuh'
CONSUMER_SECRET = '1t5FfgSfdAHHNu4brSTlCC0ecw9NG9mqBI1lqaKZZSFGOQbyqm'

def get_tumblr_profile_data(oauth_token, oauth_token_secret):
    client = pytumblr.TumblrRestClient(
        CONSUMER_KEY,
        CONSUMER_SECRET,
        oauth_token,
        oauth_token_secret
    )

    user_info = client.info()
    username = user_info['user']['name']
    display_name = user_info['user']['blogs'][0].get('title', 'No Title')
    
    blog_info = user_info['user']['blogs'][0]

    if 'avatar' in blog_info and isinstance(blog_info['avatar'], list) and len(blog_info['avatar']) > 0:
        profile_image_url = blog_info['avatar'][0]['url']
    else:
        profile_image_url = None  

    profile_data = {
        'username': username,
        'name': display_name,
        'profile_image': profile_image_url, 
        'profile_url': f'https://{username}.tumblr.com'
    }

    return profile_data