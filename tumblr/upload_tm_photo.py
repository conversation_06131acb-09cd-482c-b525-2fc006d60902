# # Photo Upload Function Only:
import pytumblr

CONSUMER_KEY = '23pJ5t0JKNSORTvsY2e9HyQF002wMpYlk01mce4V7C0yLkHFuh'
CONSUMER_SECRET = '1t5FfgSfdAHHNu4brSTlCC0ecw9NG9mqBI1lqaKZZSFGOQbyqm'



def upload_tumblr_photo(oauth_token,oauth_token_secret, photo_path, caption=None):
    client = pytumblr.TumblrRestClient(
    CONSUMER_KEY,
    CONSUMER_SECRET,
    oauth_token,
    oauth_token_secret
    )
    user_info = client.info()
    blog_name = user_info['user']['name']
    try:
        response = client.create_photo(blog_name, state='published', data=photo_path, caption=caption)
        print("Photo uploaded successfully!")
        print(response)
        return response['id'],True
    except Exception as e:
        return 'Post Not Uploaded',False

# Usage example
# photo_path = '1.jpg' 
# caption = 'This is a photo caption' 

# print(upload_tumblr_photo('oNSS0bv1hZKFYlPzhUUsBOAeXDGypcUcGE47NHalDXDCAvwcue','0EEg55YrWJyVQcIPwxT0F1PV8ll9nHHzzB0G86xrqxrEl1Dven',photo_path, caption))

def upload_tumblr_video(oauth_token,oauth_token_secret,video_path, caption=None):
    client = pytumblr.TumblrRestClient(
    CONSUMER_KEY,
    CONSUMER_SECRET,
    oauth_token,
    oauth_token_secret
    )
    try:
        user_info = client.info()
        blog_name = user_info['user']['name']
        response = client.create_video(blog_name, state='published', data=video_path, caption=caption)
        print("Video uploaded successfully!")
        print(response)
        return response['id'],True
    except Exception as e:
        print(f"Error uploading video: {e}")
        return 'Post Not Uploaded',False
# blog_name = 'nevil2212'
# video_path = '1001.mp4' 
# caption = 'This is a video caption' 

# upload_video(blog_name, video_path, caption)