import pytumblr
import requests
from requests_oauthlib import OAuth1
from urllib.parse import parse_qs

CONSUMER_KEY = '23pJ5t0JKNSORTvsY2e9HyQF002wMpYlk01mce4V7C0yLkHFuh'
CONSUMER_SECRET = '1t5FfgSfdAHHNu4brSTlCC0ecw9NG9mqBI1lqaKZZSFGOQbyqm'

def get_tumblr_analytics(access_token, oauth_token, y_post_id):
    client = pytumblr.TumblrRestClient(
        CONSUMER_KEY,
        CONSUMER_SECRET,
        access_token,
        oauth_token
    )

    try:
        user_info = client.info()
        print(user_info)
        blog_name = user_info['user']['name']
        posts = client.posts(blog_name)['posts']
        
        likes_count = 0
        comments_count = 0
        
        for post in posts:
            if post['type'] in ['photo', 'video']:
                post_id = post['id']
                if post_id == y_post_id:
                    post_url = post['post_url']
                    post_url = post_url.replace('tumblr.com/', 'tumblr.com/medium/')  # Change URL size to medium
                    notes = client.notes(blog_name, post_id)['notes']

                    for note in notes:
                        if note['type'] == 'like':
                            likes_count += 1
                        elif note['type'] == 'reply':
                            comments_count += 1
                    # Return likes and comments once the specific post is found
                    return likes_count, comments_count

        # If the post wasn't found, return counts as zero
        return likes_count, comments_count

    except Exception as e:
        print(f"Error retrieving posts: {e}")
        # Return None or default values to avoid unpacking errors
        return 0, 0


# print(get_tumblr_analytics('A5dOsyAtMz3RmQWPTz1SWYt45mcqkqz8kq54NZu5hKeR7wJcse','StBK2pOsqiFwL6tfJ7eVc4J7gwC8oDFPvmvG2NSULcgP4efFxb',764308627354009600))
