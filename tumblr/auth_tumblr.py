from requests_oauthlib import OAuth1Session
import urllib


# Replace with your Tumblr app credentials
client_key = "23pJ5t0JKNSORTvsY2e9HyQF002wMpYlk01mce4V7C0yLkHFuh"
client_secret = "1t5FfgSfdAHHNu4brSTlCC0ecw9NG9mqBI1lqaKZZSFGOQbyqm"
request_token_url = "https://www.tumblr.com/oauth/request_token"
authorization_base_url = "https://www.tumblr.com/oauth/authorize"
access_token_url = "https://www.tumblr.com/oauth/access_token"
# redirect_uri = 'https://staging.flowkar.com/api/tumblr-auth/'
redirect_uri = 'https://api.flowkar.com/api/tumblr-auth/'
user_id=1
url = f'www.tumblr.com/oauth2/authorize?client_key={client_secret}&response_type=code&scope=basic&state={user_id}&redirect_uri={redirect_uri}'

print(url)