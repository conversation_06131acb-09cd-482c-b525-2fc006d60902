import requests

API_KEY = 'key_test_cfc870586cd84215b30364bb9f17c5f6'
API_SECRET = 'secret_test_63ec94537a0244efa4d2a9df20602015'
def kyc_auth():
    url = "https://api.sandbox.co.in/authenticate"

    headers = {
        "accept": "application/json",
        "x-api-key": API_KEY,
        "x-api-secret": API_SECRET
    }

    response = requests.post(url, headers=headers)
    if response.status_code == 200:
        return True,response.json()['data']['access_token']
    else:
        return False,response.text

def kyc_generate_adhar_otp(adhar_number):

    auth_url = "https://api.sandbox.co.in/authenticate"

    headers = {
        "accept": "application/json",
        "x-api-key": API_KEY,
        "x-api-secret": API_SECRET
    }

    auth_response = requests.post(auth_url, headers=headers)
    if auth_response.status_code == 200:
        auth_token = auth_response.json()['data']['access_token']
    else:
        auth_token = None
    

    url = "https://api.sandbox.co.in/kyc/aadhaar/okyc/otp"

    payload = {
        "@entity": "in.co.sandbox.kyc.aadhaar.okyc.otp.request",
        "consent": "y",
        "aadhaar_number": adhar_number,
        "reason": "For KYC"
    }
    headers = {
        "accept": "application/json",
        "authorization": auth_token,
        "x-api-key": API_KEY,
        "x-api-version": "2.0",
        "content-type": "application/json"
    }

    response = requests.post(url, json=payload, headers=headers)
    if response.status_code == 200:
        return True,response.json()['data']['reference_id']
    else:
        return False,response.json()

def kyc_verify_adhar_otp(ref_id,otp):
    auth_url = "https://api.sandbox.co.in/authenticate"

    headers = {
        "accept": "application/json",
        "x-api-key": API_KEY,
        "x-api-secret": API_SECRET
    }

    auth_response = requests.post(auth_url, headers=headers)
    if auth_response.status_code == 200:
        auth_token = auth_response.json()['data']['access_token']
    else:
        auth_token = None

    url = "https://api.sandbox.co.in/kyc/aadhaar/okyc/otp/verify"

    payload = {
        "@entity": "in.co.sandbox.kyc.aadhaar.okyc.request",
        "reference_id":ref_id,
        "otp": otp
    }
    headers = {
        "accept": "application/json",
        "authorization": auth_token,
        "x-api-key": API_KEY,
        "x-api-version": "2.0",
        "content-type": "application/json"
    }

    response = requests.post(url, json=payload, headers=headers)
    if response.status_code == 200:
        data = {
            'status':response.json()['data']['status'],
            'official_name':response.json()['data']['name'],
            'address':response.json()['data']['full_address'],
            'dob':response.json()['data']['date_of_birth'],

        }
        return True,data
    else:
        return False,response.json()
    

def verify_pan_details(pan_number,name,dob):
    auth_url = "https://api.sandbox.co.in/authenticate"

    headers = {
        "accept": "application/json",
        "x-api-key": API_KEY,
        "x-api-secret": API_SECRET
    }

    auth_response = requests.post(auth_url, headers=headers)
    if auth_response.status_code == 200:
        auth_token = auth_response.json()['data']['access_token']
    else:
        auth_token = None

    url = "https://api.sandbox.co.in/kyc/pan/verify"

    payload = {
        "@entity": "in.co.sandbox.kyc.pan_verification.request",
        "pan": pan_number,
        "name_as_per_pan": name,
        "date_of_birth": dob,
        "consent": "Y",
        "reason": "For Tax Verification"
    }
    headers = {
        "accept": "application/json",
        "authorization": auth_token,
        "x-api-key": API_KEY,
        "x-accept-cache": "true",
        "content-type": "application/json"
    }

    response = requests.post(url, json=payload, headers=headers)
    if response.status_code == 200:
        return True,response.json()['data']
    else:
        return False,response.json()