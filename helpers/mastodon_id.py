import re

def extract_mastodon_id(mastodon_string):
    """
    Extract the Mastodon ID from a string in format:
    id|access_token|refresh_token|instance_url
    
    Args:
        mastodon_string (str): String containing Mastodon credentials
        
    Returns:
        str: The Mastodon ID or None if not found
    """
    # Regex pattern to match the ID (first part before the first pipe)
    pattern = r'^([^|]+)'
    
    match = re.match(pattern, mastodon_string)
    if match:
        return match.group(1)
    return None
