
from Authentication.models import UserRegistration


def get_profile_completion_status(user_id):
    try:
        user = UserRegistration.objects.get(id=user_id)
    except UserRegistration.DoesNotExist:
        return False, 0 

    fields_to_check = [
        user.bio,
        user.dob,
        user.phone,
        user.gender,
        user.country,
        user.state,
        user.city,
        user.profile_picture
    ]

    total_fields = len(fields_to_check)
    completed_fields = 0

    for field in fields_to_check:
        if isinstance(field, str):
            if field.strip():
                completed_fields += 1
        elif field:
            completed_fields += 1

    percentage = int((completed_fields / total_fields) * 100)

    if percentage == 100:
        return True, 100
    return False, percentage