from PIL import Image

def convert_to_jpg(input_path, output_path):
    # Open an image file
    with Image.open(input_path) as img:
        # Convert the image to RGB mode if it is not in that mode
        if img.mode in ("RGBA", "P"):
            img = img.convert("RGB")
        # Save the image in JPG format
        img.save(output_path, "JPEG")

# Example usage
input_image_path = "2.PNG"
output_image_path = "2.jpg"
convert_to_jpg(input_image_path, output_image_path)
