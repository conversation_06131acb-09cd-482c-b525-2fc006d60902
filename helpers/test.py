
import requests


INSTAGRAM_CLIENT_ID = '398617479917614'
INSTAGRAM_CLIENT_SECRET = 'fb7115ff8c017d7d2830aedc3147578b'
INSTAGRAM_REDIRECT_URI = 'https://staging.yooii.com/api/instagram/callback/'
INSTAGRAM_AUTH_URL = 'https://api.instagram.com/oauth/authorize'
INSTAGRAM_TOKEN_URL = 'https://api.instagram.com/oauth/access_token'
INSTAGRAM_API_URL = 'https://graph.instagram.com/me'
def insta(code):
        # code = request.GET.get('code')
        print(code)
        if not code:
            print({"error": "Authorization code not provided"}, status=400)

        # Exchange authorization code for access token
        data = {
            'client_id':INSTAGRAM_CLIENT_ID,
            'client_secret': INSTAGRAM_CLIENT_SECRET,
            'grant_type': 'authorization_code',
            'redirect_uri': INSTAGRAM_REDIRECT_URI,
            'code': code,
        }
        print(data)

        # Request Instagram for access token
        token_response = requests.post(INSTAGRAM_TOKEN_URL, data=data)
        token_data = token_response.json()
        print(token_data)

        if 'access_token' in token_data:
            access_token = token_data['access_token']
            
            # Fetch user info from Instagram
            user_info_response = requests.get(
                f'{INSTAGRAM_API_URL}?fields=id,username&access_token={access_token}'
            )
            user_info = user_info_response.json()

            print({
                    'access_token': access_token,
                    'user_info': user_info
                })
        else:
            print({"error": "Failed to obtain access token"})


print(insta('AQCqerAE5YirQlDAhczC42CopoqKJ2UV9-dgU06Hmf_KqlrpqIguaDoJkl0u_HkKqQGriKlU4Qf77Hn7E0yKqlRYf35rt0Qv2oFILPAyAxVqZiHVS_GLskbRbrmNb94VrqN3AQ1zsDpOFcgNc0gFNPnzhOmmICjw7ugZBzThHG0kJh3DXGPMky8Ewcp-lRP3xIb5pOra2SF0fW1Oxy2gJuQTmxNco1H-UbLm63B8PfKL8g#_'))