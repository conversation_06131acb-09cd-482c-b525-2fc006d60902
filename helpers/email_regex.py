import re

def is_valid_email(email):
    # Define a regex pattern for validating an email
    email_regex = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    return re.match(email_regex, email) is not None


def extract_organization_id(urn: str) -> str:
    match = re.search(r"\d+$", urn)
    return match.group() if match else None


def extract_after_hyphen(s):
    match = re.search(r'-(\S+)', s)
    return match.group(1) if match else None

def extract_vimeo_id(id):
    match = re.search(r"(?<=/videos/)\d+", id)
    if match:
        return match.group()
    else:
        return ''