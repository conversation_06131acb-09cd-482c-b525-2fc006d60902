from datetime import datetime

def convert_to_milliseconds(start_date: str, end_date: str):
    # Convert date strings (dd-mm-yyyy) to datetime objects
    start_dt = datetime.strptime(start_date, "%d-%m-%Y")
    end_dt = datetime.strptime(end_date, "%d-%m-%Y")
    
    # Convert to Unix timestamp (milliseconds)
    start_timestamp = int(start_dt.timestamp() * 1000)
    end_timestamp = int(end_dt.timestamp() * 1000)
    
    # Return formatted query string
    return f"&timeIntervals.timeGranularityType=DAY&timeIntervals.timeRange.start={start_timestamp}&timeIntervals.timeRange.end={end_timestamp}"