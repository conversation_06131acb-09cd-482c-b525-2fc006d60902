id,name,iso3,iso2,numeric_code,phonecode,capital,currency,currency_name,currency_symbol,tld,native,region,region_id,subregion,subregion_id,nationality,timezones,latitude,longitude,emoji,emojiU
1,Afghanistan,AFG,AF,004,93,Kabul,AFN,"Afghan afghani",؋,.af,افغانستان,Asia,3,"Southern Asia",14,Afghan,"[{zoneName:'Asia\/Kabul',gmtOffset:16200,gmtOffsetName:'UTC+04:30',abbreviation:'AFT',tzName:'Afghanistan Time'}]",33.00000000,65.00000000,🇦🇫,"U+1F1E6 U+1F1EB"
2,"Aland Islands",ALA,AX,248,358,Mariehamn,EUR,Euro,€,.ax,Åland,Europe,4,"Northern Europe",18,"Aland Island","[{zoneName:'Europe\/Mariehamn',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",60.11666700,19.90000000,🇦🇽,"U+1F1E6 U+1F1FD"
3,Albania,ALB,AL,008,355,Tirana,ALL,"Albanian lek",Lek,.al,Shqipëria,Europe,4,"Southern Europe",16,"Albanian ","[{zoneName:'Europe\/Tirane',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",41.00000000,20.00000000,🇦🇱,"U+1F1E6 U+1F1F1"
4,Algeria,DZA,DZ,012,213,Algiers,DZD,"Algerian dinar",دج,.dz,الجزائر,Africa,1,"Northern Africa",1,Algerian,"[{zoneName:'Africa\/Algiers',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",28.00000000,3.00000000,🇩🇿,"U+1F1E9 U+1F1FF"
5,"American Samoa",ASM,AS,016,1,"Pago Pago",USD,"United States dollar",$,.as,"American Samoa",Oceania,5,Polynesia,22,"American Samoan","[{zoneName:'Pacific\/Pago_Pago',gmtOffset:-39600,gmtOffsetName:'UTC-11:00',abbreviation:'SST',tzName:'Samoa Standard Time'}]",-14.33333333,-170.00000000,🇦🇸,"U+1F1E6 U+1F1F8"
6,Andorra,AND,AD,020,376,"Andorra la Vella",EUR,Euro,€,.ad,Andorra,Europe,4,"Southern Europe",16,Andorran,"[{zoneName:'Europe\/Andorra',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",42.50000000,1.50000000,🇦🇩,"U+1F1E6 U+1F1E9"
7,Angola,AGO,AO,024,244,Luanda,AOA,"Angolan kwanza",Kz,.ao,Angola,Africa,1,"Middle Africa",2,Angolan,"[{zoneName:'Africa\/Luanda',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",-12.50000000,18.50000000,🇦🇴,"U+1F1E6 U+1F1F4"
8,Anguilla,AIA,AI,660,1,"The Valley",XCD,"Eastern Caribbean dollar",$,.ai,Anguilla,Americas,2,Caribbean,7,Anguillan,"[{zoneName:'America\/Anguilla',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",18.25000000,-63.16666666,🇦🇮,"U+1F1E6 U+1F1EE"
9,Antarctica,ATA,AQ,010,672,,AAD,"Antarctican dollar",$,.aq,Antarctica,Polar,6,,0,Antarctic,"[{zoneName:'Antarctica\/Casey',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'AWST',tzName:'Australian Western Standard Time'},{zoneName:'Antarctica\/Davis',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'DAVT',tzName:'Davis Time'},{zoneName:'Antarctica\/DumontDUrville',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'DDUT',tzName:'Dumont d'Urville Time'},{zoneName:'Antarctica\/Mawson',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'MAWT',tzName:'Mawson Station Time'},{zoneName:'Antarctica\/McMurdo',gmtOffset:46800,gmtOffsetName:'UTC+13:00',abbreviation:'NZDT',tzName:'New Zealand Daylight Time'},{zoneName:'Antarctica\/Palmer',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'CLST',tzName:'Chile Summer Time'},{zoneName:'Antarctica\/Rothera',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ROTT',tzName:'Rothera Research Station Time'},{zoneName:'Antarctica\/Syowa',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'SYOT',tzName:'Showa Station Time'},{zoneName:'Antarctica\/Troll',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'},{zoneName:'Antarctica\/Vostok',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'VOST',tzName:'Vostok Station Time'}]",-74.65000000,4.48000000,🇦🇶,"U+1F1E6 U+1F1F6"
10,"Antigua and Barbuda",ATG,AG,028,1,"St. John's",XCD,"Eastern Caribbean dollar",$,.ag,"Antigua and Barbuda",Americas,2,Caribbean,7,"Antiguan or Barbudan","[{zoneName:'America\/Antigua',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",17.05000000,-61.80000000,🇦🇬,"U+1F1E6 U+1F1EC"
11,Argentina,ARG,AR,032,54,"Buenos Aires",ARS,"Argentine peso",$,.ar,Argentina,Americas,2,"South America",8,Argentine,"[{zoneName:'America\/Argentina\/Buenos_Aires',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Catamarca',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Cordoba',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Jujuy',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/La_Rioja',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Mendoza',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Rio_Gallegos',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Salta',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/San_Juan',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/San_Luis',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Tucuman',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'},{zoneName:'America\/Argentina\/Ushuaia',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'ART',tzName:'Argentina Time'}]",-34.00000000,-64.00000000,🇦🇷,"U+1F1E6 U+1F1F7"
12,Armenia,ARM,AM,051,374,Yerevan,AMD,"Armenian dram",֏,.am,Հայաստան,Asia,3,"Western Asia",11,Armenian,"[{zoneName:'Asia\/Yerevan',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'AMT',tzName:'Armenia Time'}]",40.00000000,45.00000000,🇦🇲,"U+1F1E6 U+1F1F2"
13,Aruba,ABW,AW,533,297,Oranjestad,AWG,"Aruban florin",ƒ,.aw,Aruba,Americas,2,Caribbean,7,Aruban,"[{zoneName:'America\/Aruba',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",12.50000000,-69.96666666,🇦🇼,"U+1F1E6 U+1F1FC"
14,Australia,AUS,AU,036,61,Canberra,AUD,"Australian dollar",$,.au,Australia,Oceania,5,"Australia and New Zealand",19,Australian,"[{zoneName:'Antarctica\/Macquarie',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'MIST',tzName:'Macquarie Island Station Time'},{zoneName:'Australia\/Adelaide',gmtOffset:37800,gmtOffsetName:'UTC+10:30',abbreviation:'ACDT',tzName:'Australian Central Daylight Saving Time'},{zoneName:'Australia\/Brisbane',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'AEST',tzName:'Australian Eastern Standard Time'},{zoneName:'Australia\/Broken_Hill',gmtOffset:37800,gmtOffsetName:'UTC+10:30',abbreviation:'ACDT',tzName:'Australian Central Daylight Saving Time'},{zoneName:'Australia\/Currie',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'AEDT',tzName:'Australian Eastern Daylight Saving Time'},{zoneName:'Australia\/Darwin',gmtOffset:34200,gmtOffsetName:'UTC+09:30',abbreviation:'ACST',tzName:'Australian Central Standard Time'},{zoneName:'Australia\/Eucla',gmtOffset:31500,gmtOffsetName:'UTC+08:45',abbreviation:'ACWST',tzName:'Australian Central Western Standard Time (Unofficial)'},{zoneName:'Australia\/Hobart',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'AEDT',tzName:'Australian Eastern Daylight Saving Time'},{zoneName:'Australia\/Lindeman',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'AEST',tzName:'Australian Eastern Standard Time'},{zoneName:'Australia\/Lord_Howe',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'LHST',tzName:'Lord Howe Summer Time'},{zoneName:'Australia\/Melbourne',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'AEDT',tzName:'Australian Eastern Daylight Saving Time'},{zoneName:'Australia\/Perth',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'AWST',tzName:'Australian Western Standard Time'},{zoneName:'Australia\/Sydney',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'AEDT',tzName:'Australian Eastern Daylight Saving Time'}]",-27.00000000,133.00000000,🇦🇺,"U+1F1E6 U+1F1FA"
15,Austria,AUT,AT,040,43,Vienna,EUR,Euro,€,.at,Österreich,Europe,4,"Western Europe",17,Austrian,"[{zoneName:'Europe\/Vienna',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",47.33333333,13.33333333,🇦🇹,"U+1F1E6 U+1F1F9"
16,Azerbaijan,AZE,AZ,031,994,Baku,AZN,"Azerbaijani manat",m,.az,Azərbaycan,Asia,3,"Western Asia",11,"Azerbaijani, Azeri","[{zoneName:'Asia\/Baku',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'AZT',tzName:'Azerbaijan Time'}]",40.50000000,47.50000000,🇦🇿,"U+1F1E6 U+1F1FF"
18,Bahrain,BHR,BH,048,973,Manama,BHD,"Bahraini dinar",.د.ب,.bh,‏البحرين,Asia,3,"Western Asia",11,Bahraini,"[{zoneName:'Asia\/Bahrain',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'AST',tzName:'Arabia Standard Time'}]",26.00000000,50.55000000,🇧🇭,"U+1F1E7 U+1F1ED"
19,Bangladesh,BGD,BD,050,880,Dhaka,BDT,"Bangladeshi taka",৳,.bd,Bangladesh,Asia,3,"Southern Asia",14,Bangladeshi,"[{zoneName:'Asia\/Dhaka',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'BDT',tzName:'Bangladesh Standard Time'}]",24.00000000,90.00000000,🇧🇩,"U+1F1E7 U+1F1E9"
20,Barbados,BRB,BB,052,1,Bridgetown,BBD,"Barbadian dollar",Bds$,.bb,Barbados,Americas,2,Caribbean,7,Barbadian,"[{zoneName:'America\/Barbados',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",13.16666666,-59.53333333,🇧🇧,"U+1F1E7 U+1F1E7"
21,Belarus,BLR,BY,112,375,Minsk,BYN,"Belarusian ruble",Br,.by,Белару́сь,Europe,4,"Eastern Europe",15,Belarusian,"[{zoneName:'Europe\/Minsk',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'MSK',tzName:'Moscow Time'}]",53.00000000,28.00000000,🇧🇾,"U+1F1E7 U+1F1FE"
22,Belgium,BEL,BE,056,32,Brussels,EUR,Euro,€,.be,België,Europe,4,"Western Europe",17,Belgian,"[{zoneName:'Europe\/Brussels',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",50.83333333,4.00000000,🇧🇪,"U+1F1E7 U+1F1EA"
23,Belize,BLZ,BZ,084,501,Belmopan,BZD,"Belize dollar",$,.bz,Belize,Americas,2,"Central America",9,Belizean,"[{zoneName:'America\/Belize',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America)'}]",17.25000000,-88.75000000,🇧🇿,"U+1F1E7 U+1F1FF"
24,Benin,BEN,BJ,204,229,Porto-Novo,XOF,"West African CFA franc",CFA,.bj,Bénin,Africa,1,"Western Africa",3,"Beninese, Beninois","[{zoneName:'Africa\/Porto-Novo',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",9.50000000,2.25000000,🇧🇯,"U+1F1E7 U+1F1EF"
25,Bermuda,BMU,BM,060,1,Hamilton,BMD,"Bermudian dollar",$,.bm,Bermuda,Americas,2,"Northern America",6,"Bermudian, Bermudan","[{zoneName:'Atlantic\/Bermuda',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",32.33333333,-64.75000000,🇧🇲,"U+1F1E7 U+1F1F2"
26,Bhutan,BTN,BT,064,975,Thimphu,BTN,"Bhutanese ngultrum",Nu.,.bt,ʼbrug-yul,Asia,3,"Southern Asia",14,Bhutanese,"[{zoneName:'Asia\/Thimphu',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'BTT',tzName:'Bhutan Time'}]",27.50000000,90.50000000,🇧🇹,"U+1F1E7 U+1F1F9"
27,Bolivia,BOL,BO,068,591,Sucre,BOB,"Bolivian boliviano",Bs.,.bo,Bolivia,Americas,2,"South America",8,Bolivian,"[{zoneName:'America\/La_Paz',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'BOT',tzName:'Bolivia Time'}]",-17.00000000,-65.00000000,🇧🇴,"U+1F1E7 U+1F1F4"
155,"Bonaire, Sint Eustatius and Saba",BES,BQ,535,599,Kralendijk,USD,"United States dollar",$,.an,"Caribisch Nederland",Americas,2,Caribbean,7,Bonaire,"[{zoneName:'America\/Anguilla',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",12.15000000,-68.26666700,🇧🇶,"U+1F1E7 U+1F1F6"
28,"Bosnia and Herzegovina",BIH,BA,070,387,Sarajevo,BAM,"Bosnia and Herzegovina convertible mark",KM,.ba,"Bosna i Hercegovina",Europe,4,"Southern Europe",16,"Bosnian or Herzegovinian","[{zoneName:'Europe\/Sarajevo',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",44.00000000,18.00000000,🇧🇦,"U+1F1E7 U+1F1E6"
29,Botswana,BWA,BW,072,267,Gaborone,BWP,"Botswana pula",P,.bw,Botswana,Africa,1,"Southern Africa",5,"Motswana, Botswanan","[{zoneName:'Africa\/Gaborone',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",-22.00000000,24.00000000,🇧🇼,"U+1F1E7 U+1F1FC"
30,"Bouvet Island",BVT,BV,074,0055,,NOK,"Norwegian krone",ko,.bv,Bouvetøya,,0,,0,"Bouvet Island","[{zoneName:'Europe\/Oslo',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",-54.43333333,3.40000000,🇧🇻,"U+1F1E7 U+1F1FB"
31,Brazil,BRA,BR,076,55,Brasilia,BRL,"Brazilian real",R$,.br,Brasil,Americas,2,"South America",8,Brazilian,"[{zoneName:'America\/Araguaina',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'},{zoneName:'America\/Bahia',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'},{zoneName:'America\/Belem',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'},{zoneName:'America\/Boa_Vista',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AMT',tzName:'Amazon Time (Brazil)[3'},{zoneName:'America\/Campo_Grande',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AMT',tzName:'Amazon Time (Brazil)[3'},{zoneName:'America\/Cuiaba',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'BRT',tzName:'Brasilia Time'},{zoneName:'America\/Eirunepe',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'ACT',tzName:'Acre Time'},{zoneName:'America\/Fortaleza',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'},{zoneName:'America\/Maceio',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'},{zoneName:'America\/Manaus',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AMT',tzName:'Amazon Time (Brazil)'},{zoneName:'America\/Noronha',gmtOffset:-7200,gmtOffsetName:'UTC-02:00',abbreviation:'FNT',tzName:'Fernando de Noronha Time'},{zoneName:'America\/Porto_Velho',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AMT',tzName:'Amazon Time (Brazil)[3'},{zoneName:'America\/Recife',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'},{zoneName:'America\/Rio_Branco',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'ACT',tzName:'Acre Time'},{zoneName:'America\/Santarem',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'},{zoneName:'America\/Sao_Paulo',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'BRT',tzName:'Bras\u00edlia Time'}]",-10.00000000,-55.00000000,🇧🇷,"U+1F1E7 U+1F1F7"
32,"British Indian Ocean Territory",IOT,IO,086,246,"Diego Garcia",USD,"United States dollar",$,.io,"British Indian Ocean Territory",Africa,1,"Eastern Africa",4,BIOT,"[{zoneName:'Indian\/Chagos',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'IOT',tzName:'Indian Ocean Time'}]",-6.00000000,71.50000000,🇮🇴,"U+1F1EE U+1F1F4"
33,Brunei,BRN,BN,096,673,"Bandar Seri Begawan",BND,"Brunei dollar",B$,.bn,"Negara Brunei Darussalam",Asia,3,"South-Eastern Asia",13,Bruneian,"[{zoneName:'Asia\/Brunei',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'BNT',tzName:'Brunei Darussalam Time'}]",4.50000000,114.66666666,🇧🇳,"U+1F1E7 U+1F1F3"
34,Bulgaria,BGR,BG,100,359,Sofia,BGN,"Bulgarian lev",Лв.,.bg,България,Europe,4,"Eastern Europe",15,Bulgarian,"[{zoneName:'Europe\/Sofia',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",43.00000000,25.00000000,🇧🇬,"U+1F1E7 U+1F1EC"
35,"Burkina Faso",BFA,BF,854,226,Ouagadougou,XOF,"West African CFA franc",CFA,.bf,"Burkina Faso",Africa,1,"Western Africa",3,Burkinabe,"[{zoneName:'Africa\/Ouagadougou',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",13.00000000,-2.00000000,🇧🇫,"U+1F1E7 U+1F1EB"
36,Burundi,BDI,BI,108,257,Bujumbura,BIF,"Burundian franc",FBu,.bi,Burundi,Africa,1,"Eastern Africa",4,Burundian,"[{zoneName:'Africa\/Bujumbura',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",-3.50000000,30.00000000,🇧🇮,"U+1F1E7 U+1F1EE"
37,Cambodia,KHM,KH,116,855,"Phnom Penh",KHR,"Cambodian riel",KHR,.kh,Kâmpŭchéa,Asia,3,"South-Eastern Asia",13,Cambodian,"[{zoneName:'Asia\/Phnom_Penh',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'ICT',tzName:'Indochina Time'}]",13.00000000,105.00000000,🇰🇭,"U+1F1F0 U+1F1ED"
38,Cameroon,CMR,CM,120,237,Yaounde,XAF,"Central African CFA franc",FCFA,.cm,Cameroon,Africa,1,"Middle Africa",2,Cameroonian,"[{zoneName:'Africa\/Douala',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",6.00000000,12.00000000,🇨🇲,"U+1F1E8 U+1F1F2"
39,Canada,CAN,CA,124,1,Ottawa,CAD,"Canadian dollar",$,.ca,Canada,Americas,2,"Northern America",6,Canadian,"[{zoneName:'America\/Atikokan',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America)'},{zoneName:'America\/Blanc-Sablon',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'},{zoneName:'America\/Cambridge_Bay',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America)'},{zoneName:'America\/Creston',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America)'},{zoneName:'America\/Dawson',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America)'},{zoneName:'America\/Dawson_Creek',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America)'},{zoneName:'America\/Edmonton',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America)'},{zoneName:'America\/Fort_Nelson',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America)'},{zoneName:'America\/Glace_Bay',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'},{zoneName:'America\/Goose_Bay',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'},{zoneName:'America\/Halifax',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'},{zoneName:'America\/Inuvik',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Iqaluit',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Moncton',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'},{zoneName:'America\/Nipigon',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Pangnirtung',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Rainy_River',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Rankin_Inlet',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Regina',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Resolute',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/St_Johns',gmtOffset:-12600,gmtOffsetName:'UTC-03:30',abbreviation:'NST',tzName:'Newfoundland Standard Time'},{zoneName:'America\/Swift_Current',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Thunder_Bay',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Toronto',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Vancouver',gmtOffset:-28800,gmtOffsetName:'UTC-08:00',abbreviation:'PST',tzName:'Pacific Standard Time (North America'},{zoneName:'America\/Whitehorse',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Winnipeg',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Yellowknife',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'}]",60.00000000,-95.00000000,🇨🇦,"U+1F1E8 U+1F1E6"
40,"Cape Verde",CPV,CV,132,238,Praia,CVE,"Cape Verdean escudo",$,.cv,"Cabo Verde",Africa,1,"Western Africa",3,Verdean,"[{zoneName:'Atlantic\/Cape_Verde',gmtOffset:-3600,gmtOffsetName:'UTC-01:00',abbreviation:'CVT',tzName:'Cape Verde Time'}]",16.00000000,-24.00000000,🇨🇻,"U+1F1E8 U+1F1FB"
41,"Cayman Islands",CYM,KY,136,1,"George Town",KYD,"Cayman Islands dollar",$,.ky,"Cayman Islands",Americas,2,Caribbean,7,Caymanian,"[{zoneName:'America\/Cayman',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'}]",19.50000000,-80.50000000,🇰🇾,"U+1F1F0 U+1F1FE"
42,"Central African Republic",CAF,CF,140,236,Bangui,XAF,"Central African CFA franc",FCFA,.cf,"Ködörösêse tî Bêafrîka",Africa,1,"Middle Africa",2,"Central African","[{zoneName:'Africa\/Bangui',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",7.00000000,21.00000000,🇨🇫,"U+1F1E8 U+1F1EB"
43,Chad,TCD,TD,148,235,N'Djamena,XAF,"Central African CFA franc",FCFA,.td,Tchad,Africa,1,"Middle Africa",2,Chadian,"[{zoneName:'Africa\/Ndjamena',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",15.00000000,19.00000000,🇹🇩,"U+1F1F9 U+1F1E9"
44,Chile,CHL,CL,152,56,Santiago,CLP,"Chilean peso",$,.cl,Chile,Americas,2,"South America",8,Chilean,"[{zoneName:'America\/Punta_Arenas',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'CLST',tzName:'Chile Summer Time'},{zoneName:'America\/Santiago',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'CLST',tzName:'Chile Summer Time'},{zoneName:'Pacific\/Easter',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EASST',tzName:'Easter Island Summer Time'}]",-30.00000000,-71.00000000,🇨🇱,"U+1F1E8 U+1F1F1"
45,China,CHN,CN,156,86,Beijing,CNY,"Chinese yuan",¥,.cn,中国,Asia,3,"Eastern Asia",12,Chinese,"[{zoneName:'Asia\/Shanghai',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'CST',tzName:'China Standard Time'},{zoneName:'Asia\/Urumqi',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'XJT',tzName:'China Standard Time'}]",35.00000000,105.00000000,🇨🇳,"U+1F1E8 U+1F1F3"
46,"Christmas Island",CXR,CX,162,61,"Flying Fish Cove",AUD,"Australian dollar",$,.cx,"Christmas Island",Oceania,5,"Australia and New Zealand",19,"Christmas Island","[{zoneName:'Indian\/Christmas',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'CXT',tzName:'Christmas Island Time'}]",-10.50000000,105.66666666,🇨🇽,"U+1F1E8 U+1F1FD"
47,"Cocos (Keeling) Islands",CCK,CC,166,61,"West Island",AUD,"Australian dollar",$,.cc,"Cocos (Keeling) Islands",Oceania,5,"Australia and New Zealand",19,"Cocos Island","[{zoneName:'Indian\/Cocos',gmtOffset:23400,gmtOffsetName:'UTC+06:30',abbreviation:'CCT',tzName:'Cocos Islands Time'}]",-12.50000000,96.83333333,🇨🇨,"U+1F1E8 U+1F1E8"
48,Colombia,COL,CO,170,57,Bogotá,COP,"Colombian peso",$,.co,Colombia,Americas,2,"South America",8,Colombian,"[{zoneName:'America\/Bogota',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'COT',tzName:'Colombia Time'}]",4.00000000,-72.00000000,🇨🇴,"U+1F1E8 U+1F1F4"
49,Comoros,COM,KM,174,269,Moroni,KMF,"Comorian franc",CF,.km,Komori,Africa,1,"Eastern Africa",4,"Comoran, Comorian","[{zoneName:'Indian\/Comoro',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",-12.16666666,44.25000000,🇰🇲,"U+1F1F0 U+1F1F2"
50,Congo,COG,CG,178,242,Brazzaville,XAF,"Congolese Franc",CDF,.cg,"République du Congo",Africa,1,"Middle Africa",2,Congolese,"[{zoneName:'Africa\/Brazzaville',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",-1.00000000,15.00000000,🇨🇬,"U+1F1E8 U+1F1EC"
52,"Cook Islands",COK,CK,184,682,Avarua,NZD,"New Zealand dollar",$,.ck,"Cook Islands",Oceania,5,Polynesia,22,"Cook Island","[{zoneName:'Pacific\/Rarotonga',gmtOffset:-36000,gmtOffsetName:'UTC-10:00',abbreviation:'CKT',tzName:'Cook Island Time'}]",-21.23333333,-159.76666666,🇨🇰,"U+1F1E8 U+1F1F0"
53,"Costa Rica",CRI,CR,188,506,"San Jose",CRC,"Costa Rican colón",₡,.cr,"Costa Rica",Americas,2,"Central America",9,"Costa Rican","[{zoneName:'America\/Costa_Rica',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'}]",10.00000000,-84.00000000,🇨🇷,"U+1F1E8 U+1F1F7"
54,"Cote D'Ivoire (Ivory Coast)",CIV,CI,384,225,Yamoussoukro,XOF,"West African CFA franc",CFA,.ci,,Africa,1,"Western Africa",3,Ivorian,"[{zoneName:'Africa\/Abidjan',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",8.00000000,-5.00000000,🇨🇮,"U+1F1E8 U+1F1EE"
55,Croatia,HRV,HR,191,385,Zagreb,EUR,Euro,€,.hr,Hrvatska,Europe,4,"Southern Europe",16,Croatian,"[{zoneName:'Europe\/Zagreb',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",45.16666666,15.50000000,🇭🇷,"U+1F1ED U+1F1F7"
56,Cuba,CUB,CU,192,53,Havana,CUP,"Cuban peso",$,.cu,Cuba,Americas,2,Caribbean,7,Cuban,"[{zoneName:'America\/Havana',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'CST',tzName:'Cuba Standard Time'}]",21.50000000,-80.00000000,🇨🇺,"U+1F1E8 U+1F1FA"
249,Curaçao,CUW,CW,531,599,Willemstad,ANG,"Netherlands Antillean guilder",ƒ,.cw,Curaçao,Americas,2,Caribbean,7,Curacaoan,"[{zoneName:'America\/Curacao',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",12.11666700,-68.93333300,🇨🇼,"U+1F1E8 U+1F1FC"
57,Cyprus,CYP,CY,196,357,Nicosia,EUR,Euro,€,.cy,Κύπρος,Europe,4,"Southern Europe",16,Cypriot,"[{zoneName:'Asia\/Famagusta',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'},{zoneName:'Asia\/Nicosia',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",35.00000000,33.00000000,🇨🇾,"U+1F1E8 U+1F1FE"
58,"Czech Republic",CZE,CZ,203,420,Prague,CZK,"Czech koruna",Kč,.cz,"Česká republika",Europe,4,"Eastern Europe",15,Czech,"[{zoneName:'Europe\/Prague',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",49.75000000,15.50000000,🇨🇿,"U+1F1E8 U+1F1FF"
51,"Democratic Republic of the Congo",COD,CD,180,243,Kinshasa,CDF,"Congolese Franc",FC,.cd,"République démocratique du Congo",Africa,1,"Middle Africa",2,Congolese,"[{zoneName:'Africa\/Kinshasa',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'},{zoneName:'Africa\/Lubumbashi',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",0.00000000,25.00000000,🇨🇩,"U+1F1E8 U+1F1E9"
59,Denmark,DNK,DK,208,45,Copenhagen,DKK,"Danish krone",Kr.,.dk,Danmark,Europe,4,"Northern Europe",18,Danish,"[{zoneName:'Europe\/Copenhagen',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",56.00000000,10.00000000,🇩🇰,"U+1F1E9 U+1F1F0"
60,Djibouti,DJI,DJ,262,253,Djibouti,DJF,"Djiboutian franc",Fdj,.dj,Djibouti,Africa,1,"Eastern Africa",4,Djiboutian,"[{zoneName:'Africa\/Djibouti',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",11.50000000,43.00000000,🇩🇯,"U+1F1E9 U+1F1EF"
61,Dominica,DMA,DM,212,1,Roseau,XCD,"Eastern Caribbean dollar",$,.dm,Dominica,Americas,2,Caribbean,7,Dominican,"[{zoneName:'America\/Dominica',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",15.41666666,-61.33333333,🇩🇲,"U+1F1E9 U+1F1F2"
62,"Dominican Republic",DOM,DO,214,1,"Santo Domingo",DOP,"Dominican peso",$,.do,"República Dominicana",Americas,2,Caribbean,7,Dominican,"[{zoneName:'America\/Santo_Domingo',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",19.00000000,-70.66666666,🇩🇴,"U+1F1E9 U+1F1F4"
64,Ecuador,ECU,EC,218,593,Quito,USD,"United States dollar",$,.ec,Ecuador,Americas,2,"South America",8,Ecuadorian,"[{zoneName:'America\/Guayaquil',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'ECT',tzName:'Ecuador Time'},{zoneName:'Pacific\/Galapagos',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'GALT',tzName:'Gal\u00e1pagos Time'}]",-2.00000000,-77.50000000,🇪🇨,"U+1F1EA U+1F1E8"
65,Egypt,EGY,EG,818,20,Cairo,EGP,"Egyptian pound",ج.م,.eg,مصر‎,Africa,1,"Northern Africa",1,Egyptian,"[{zoneName:'Africa\/Cairo',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",27.00000000,30.00000000,🇪🇬,"U+1F1EA U+1F1EC"
66,"El Salvador",SLV,SV,222,503,"San Salvador",USD,"United States dollar",$,.sv,"El Salvador",Americas,2,"Central America",9,Salvadoran,"[{zoneName:'America\/El_Salvador',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'}]",13.83333333,-88.91666666,🇸🇻,"U+1F1F8 U+1F1FB"
67,"Equatorial Guinea",GNQ,GQ,226,240,Malabo,XAF,"Central African CFA franc",FCFA,.gq,"Guinea Ecuatorial",Africa,1,"Middle Africa",2,"Equatorial Guinean, Equatoguinean","[{zoneName:'Africa\/Malabo',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",2.00000000,10.00000000,🇬🇶,"U+1F1EC U+1F1F6"
68,Eritrea,ERI,ER,232,291,Asmara,ERN,"Eritrean nakfa",Nfk,.er,ኤርትራ,Africa,1,"Eastern Africa",4,Eritrean,"[{zoneName:'Africa\/Asmara',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",15.00000000,39.00000000,🇪🇷,"U+1F1EA U+1F1F7"
69,Estonia,EST,EE,233,372,Tallinn,EUR,Euro,€,.ee,Eesti,Europe,4,"Northern Europe",18,Estonian,"[{zoneName:'Europe\/Tallinn',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",59.00000000,26.00000000,🇪🇪,"U+1F1EA U+1F1EA"
212,Eswatini,SWZ,SZ,748,268,Mbabane,SZL,Lilangeni,E,.sz,Swaziland,Africa,1,"Southern Africa",5,Swazi,"[{zoneName:'Africa\/Mbabane',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'SAST',tzName:'South African Standard Time'}]",-26.50000000,31.50000000,🇸🇿,"U+1F1F8 U+1F1FF"
70,Ethiopia,ETH,ET,231,251,"Addis Ababa",ETB,"Ethiopian birr",Nkf,.et,ኢትዮጵያ,Africa,1,"Eastern Africa",4,Ethiopian,"[{zoneName:'Africa\/Addis_Ababa',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",8.00000000,38.00000000,🇪🇹,"U+1F1EA U+1F1F9"
71,"Falkland Islands",FLK,FK,238,500,Stanley,FKP,"Falkland Islands pound",£,.fk,"Falkland Islands",Americas,2,"South America",8,"Falkland Island","[{zoneName:'Atlantic\/Stanley',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'FKST',tzName:'Falkland Islands Summer Time'}]",-51.75000000,-59.00000000,🇫🇰,"U+1F1EB U+1F1F0"
72,"Faroe Islands",FRO,FO,234,298,Torshavn,DKK,"Danish krone",Kr.,.fo,Føroyar,Europe,4,"Northern Europe",18,Faroese,"[{zoneName:'Atlantic\/Faroe',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'WET',tzName:'Western European Time'}]",62.00000000,-7.00000000,🇫🇴,"U+1F1EB U+1F1F4"
73,"Fiji Islands",FJI,FJ,242,679,Suva,FJD,"Fijian dollar",FJ$,.fj,Fiji,Oceania,5,Melanesia,20,Fijian,"[{zoneName:'Pacific\/Fiji',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'FJT',tzName:'Fiji Time'}]",-18.00000000,175.00000000,🇫🇯,"U+1F1EB U+1F1EF"
74,Finland,FIN,FI,246,358,Helsinki,EUR,Euro,€,.fi,Suomi,Europe,4,"Northern Europe",18,Finnish,"[{zoneName:'Europe\/Helsinki',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",64.00000000,26.00000000,🇫🇮,"U+1F1EB U+1F1EE"
75,France,FRA,FR,250,33,Paris,EUR,Euro,€,.fr,France,Europe,4,"Western Europe",17,French,"[{zoneName:'Europe\/Paris',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",46.00000000,2.00000000,🇫🇷,"U+1F1EB U+1F1F7"
76,"French Guiana",GUF,GF,254,594,Cayenne,EUR,Euro,€,.gf,"Guyane française",Americas,2,"South America",8,"French Guianese","[{zoneName:'America\/Cayenne',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'GFT',tzName:'French Guiana Time'}]",4.00000000,-53.00000000,🇬🇫,"U+1F1EC U+1F1EB"
77,"French Polynesia",PYF,PF,258,689,Papeete,XPF,"CFP franc",₣,.pf,"Polynésie française",Oceania,5,Polynesia,22,"French Polynesia","[{zoneName:'Pacific\/Gambier',gmtOffset:-32400,gmtOffsetName:'UTC-09:00',abbreviation:'GAMT',tzName:'Gambier Islands Time'},{zoneName:'Pacific\/Marquesas',gmtOffset:-34200,gmtOffsetName:'UTC-09:30',abbreviation:'MART',tzName:'Marquesas Islands Time'},{zoneName:'Pacific\/Tahiti',gmtOffset:-36000,gmtOffsetName:'UTC-10:00',abbreviation:'TAHT',tzName:'Tahiti Time'}]",-15.00000000,-140.00000000,🇵🇫,"U+1F1F5 U+1F1EB"
78,"French Southern Territories",ATF,TF,260,262,Port-aux-Francais,EUR,Euro,€,.tf,"Territoire des Terres australes et antarctiques fr",Africa,1,"Southern Africa",5,"French Southern Territories","[{zoneName:'Indian\/Kerguelen',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'TFT',tzName:'French Southern and Antarctic Time'}]",-49.25000000,69.16700000,🇹🇫,"U+1F1F9 U+1F1EB"
79,Gabon,GAB,GA,266,241,Libreville,XAF,"Central African CFA franc",FCFA,.ga,Gabon,Africa,1,"Middle Africa",2,Gabonese,"[{zoneName:'Africa\/Libreville',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",-1.00000000,11.75000000,🇬🇦,"U+1F1EC U+1F1E6"
81,Georgia,GEO,GE,268,995,Tbilisi,GEL,"Georgian lari",ლ,.ge,საქართველო,Asia,3,"Western Asia",11,Georgian,"[{zoneName:'Asia\/Tbilisi',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'GET',tzName:'Georgia Standard Time'}]",42.00000000,43.50000000,🇬🇪,"U+1F1EC U+1F1EA"
82,Germany,DEU,DE,276,49,Berlin,EUR,Euro,€,.de,Deutschland,Europe,4,"Western Europe",17,German,"[{zoneName:'Europe\/Berlin',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'},{zoneName:'Europe\/Busingen',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",51.00000000,9.00000000,🇩🇪,"U+1F1E9 U+1F1EA"
83,Ghana,GHA,GH,288,233,Accra,GHS,"Ghanaian cedi",GH₵,.gh,Ghana,Africa,1,"Western Africa",3,Ghanaian,"[{zoneName:'Africa\/Accra',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",8.00000000,-2.00000000,🇬🇭,"U+1F1EC U+1F1ED"
84,Gibraltar,GIB,GI,292,350,Gibraltar,GIP,"Gibraltar pound",£,.gi,Gibraltar,Europe,4,"Southern Europe",16,Gibraltar,"[{zoneName:'Europe\/Gibraltar',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",36.13333333,-5.35000000,🇬🇮,"U+1F1EC U+1F1EE"
85,Greece,GRC,GR,300,30,Athens,EUR,Euro,€,.gr,Ελλάδα,Europe,4,"Southern Europe",16,"Greek, Hellenic","[{zoneName:'Europe\/Athens',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",39.00000000,22.00000000,🇬🇷,"U+1F1EC U+1F1F7"
86,Greenland,GRL,GL,304,299,Nuuk,DKK,"Danish krone",Kr.,.gl,"Kalaallit Nunaat",Americas,2,"Northern America",6,Greenlandic,"[{zoneName:'America\/Danmarkshavn',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'},{zoneName:'America\/Nuuk',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'WGT',tzName:'West Greenland Time'},{zoneName:'America\/Scoresbysund',gmtOffset:-3600,gmtOffsetName:'UTC-01:00',abbreviation:'EGT',tzName:'Eastern Greenland Time'},{zoneName:'America\/Thule',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",72.00000000,-40.00000000,🇬🇱,"U+1F1EC U+1F1F1"
87,Grenada,GRD,GD,308,1,"St. George's",XCD,"Eastern Caribbean dollar",$,.gd,Grenada,Americas,2,Caribbean,7,Grenadian,"[{zoneName:'America\/Grenada',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",12.11666666,-61.66666666,🇬🇩,"U+1F1EC U+1F1E9"
88,Guadeloupe,GLP,GP,312,590,Basse-Terre,EUR,Euro,€,.gp,Guadeloupe,Americas,2,Caribbean,7,Guadeloupe,"[{zoneName:'America\/Guadeloupe',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",16.25000000,-61.58333300,🇬🇵,"U+1F1EC U+1F1F5"
89,Guam,GUM,GU,316,1,Hagatna,USD,"United States dollar",$,.gu,Guam,Oceania,5,Micronesia,21,"Guamanian, Guambat","[{zoneName:'Pacific\/Guam',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'CHST',tzName:'Chamorro Standard Time'}]",13.46666666,144.78333333,🇬🇺,"U+1F1EC U+1F1FA"
90,Guatemala,GTM,GT,320,502,"Guatemala City",GTQ,"Guatemalan quetzal",Q,.gt,Guatemala,Americas,2,"Central America",9,Guatemalan,"[{zoneName:'America\/Guatemala',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'}]",15.50000000,-90.25000000,🇬🇹,"U+1F1EC U+1F1F9"
91,"Guernsey and Alderney",GGY,GG,831,44,"St Peter Port",GBP,"British pound",£,.gg,Guernsey,Europe,4,"Northern Europe",18,"Channel Island","[{zoneName:'Europe\/Guernsey',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",49.46666666,-2.58333333,🇬🇬,"U+1F1EC U+1F1EC"
92,Guinea,GIN,GN,324,224,Conakry,GNF,"Guinean franc",FG,.gn,Guinée,Africa,1,"Western Africa",3,Guinean,"[{zoneName:'Africa\/Conakry',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",11.00000000,-10.00000000,🇬🇳,"U+1F1EC U+1F1F3"
93,Guinea-Bissau,GNB,GW,624,245,Bissau,XOF,"West African CFA franc",CFA,.gw,Guiné-Bissau,Africa,1,"Western Africa",3,Bissau-Guinean,"[{zoneName:'Africa\/Bissau',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",12.00000000,-15.00000000,🇬🇼,"U+1F1EC U+1F1FC"
94,Guyana,GUY,GY,328,592,Georgetown,GYD,"Guyanese dollar",$,.gy,Guyana,Americas,2,"South America",8,Guyanese,"[{zoneName:'America\/Guyana',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'GYT',tzName:'Guyana Time'}]",5.00000000,-59.00000000,🇬🇾,"U+1F1EC U+1F1FE"
95,Haiti,HTI,HT,332,509,Port-au-Prince,HTG,"Haitian gourde",G,.ht,Haïti,Americas,2,Caribbean,7,Haitian,"[{zoneName:'America\/Port-au-Prince',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'}]",19.00000000,-72.41666666,🇭🇹,"U+1F1ED U+1F1F9"
96,"Heard Island and McDonald Islands",HMD,HM,334,672,,AUD,"Australian dollar",$,.hm,"Heard Island and McDonald Islands",,0,,0,"Heard Island or McDonald Islands","[{zoneName:'Indian\/Kerguelen',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'TFT',tzName:'French Southern and Antarctic Time'}]",-53.10000000,72.51666666,🇭🇲,"U+1F1ED U+1F1F2"
97,Honduras,HND,HN,340,504,Tegucigalpa,HNL,"Honduran lempira",L,.hn,Honduras,Americas,2,"Central America",9,Honduran,"[{zoneName:'America\/Tegucigalpa',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'}]",15.00000000,-86.50000000,🇭🇳,"U+1F1ED U+1F1F3"
98,"Hong Kong S.A.R.",HKG,HK,344,852,"Hong Kong",HKD,"Hong Kong dollar",$,.hk,香港,Asia,3,"Eastern Asia",12,"Hong Kong, Hong Kongese","[{zoneName:'Asia\/Hong_Kong',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'HKT',tzName:'Hong Kong Time'}]",22.25000000,114.16666666,🇭🇰,"U+1F1ED U+1F1F0"
99,Hungary,HUN,HU,348,36,Budapest,HUF,"Hungarian forint",Ft,.hu,Magyarország,Europe,4,"Eastern Europe",15,"Hungarian, Magyar","[{zoneName:'Europe\/Budapest',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",47.00000000,20.00000000,🇭🇺,"U+1F1ED U+1F1FA"
100,Iceland,ISL,IS,352,354,Reykjavik,ISK,"Icelandic króna",ko,.is,Ísland,Europe,4,"Northern Europe",18,Icelandic,"[{zoneName:'Atlantic\/Reykjavik',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",65.00000000,-18.00000000,🇮🇸,"U+1F1EE U+1F1F8"
101,India,IND,IN,356,91,"New Delhi",INR,"Indian rupee",₹,.in,भारत,Asia,3,"Southern Asia",14,Indian,"[{zoneName:'Asia\/Kolkata',gmtOffset:19800,gmtOffsetName:'UTC+05:30',abbreviation:'IST',tzName:'Indian Standard Time'}]",20.00000000,77.00000000,🇮🇳,"U+1F1EE U+1F1F3"
102,Indonesia,IDN,ID,360,62,Jakarta,IDR,"Indonesian rupiah",Rp,.id,Indonesia,Asia,3,"South-Eastern Asia",13,Indonesian,"[{zoneName:'Asia\/Jakarta',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'WIB',tzName:'Western Indonesian Time'},{zoneName:'Asia\/Jayapura',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'WIT',tzName:'Eastern Indonesian Time'},{zoneName:'Asia\/Makassar',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'WITA',tzName:'Central Indonesia Time'},{zoneName:'Asia\/Pontianak',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'WIB',tzName:'Western Indonesian Time'}]",-5.00000000,120.00000000,🇮🇩,"U+1F1EE U+1F1E9"
103,Iran,IRN,IR,364,98,Tehran,IRR,"Iranian rial",﷼,.ir,ایران,Asia,3,"Southern Asia",14,"Iranian, Persian","[{zoneName:'Asia\/Tehran',gmtOffset:12600,gmtOffsetName:'UTC+03:30',abbreviation:'IRDT',tzName:'Iran Daylight Time'}]",32.00000000,53.00000000,🇮🇷,"U+1F1EE U+1F1F7"
104,Iraq,IRQ,IQ,368,964,Baghdad,IQD,"Iraqi dinar",د.ع,.iq,العراق,Asia,3,"Western Asia",11,Iraqi,"[{zoneName:'Asia\/Baghdad',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'AST',tzName:'Arabia Standard Time'}]",33.00000000,44.00000000,🇮🇶,"U+1F1EE U+1F1F6"
105,Ireland,IRL,IE,372,353,Dublin,EUR,Euro,€,.ie,Éire,Europe,4,"Northern Europe",18,Irish,"[{zoneName:'Europe\/Dublin',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",53.00000000,-8.00000000,🇮🇪,"U+1F1EE U+1F1EA"
106,Israel,ISR,IL,376,972,Jerusalem,ILS,"Israeli new shekel",₪,.il,יִשְׂרָאֵל,Asia,3,"Western Asia",11,Israeli,"[{zoneName:'Asia\/Jerusalem',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'IST',tzName:'Israel Standard Time'}]",31.50000000,34.75000000,🇮🇱,"U+1F1EE U+1F1F1"
107,Italy,ITA,IT,380,39,Rome,EUR,Euro,€,.it,Italia,Europe,4,"Southern Europe",16,Italian,"[{zoneName:'Europe\/Rome',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",42.83333333,12.83333333,🇮🇹,"U+1F1EE U+1F1F9"
108,Jamaica,JAM,JM,388,1,Kingston,JMD,"Jamaican dollar",J$,.jm,Jamaica,Americas,2,Caribbean,7,Jamaican,"[{zoneName:'America\/Jamaica',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'}]",18.25000000,-77.50000000,🇯🇲,"U+1F1EF U+1F1F2"
109,Japan,JPN,JP,392,81,Tokyo,JPY,"Japanese yen",¥,.jp,日本,Asia,3,"Eastern Asia",12,Japanese,"[{zoneName:'Asia\/Tokyo',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'JST',tzName:'Japan Standard Time'}]",36.00000000,138.00000000,🇯🇵,"U+1F1EF U+1F1F5"
110,Jersey,JEY,JE,832,44,"Saint Helier",GBP,"British pound",£,.je,Jersey,Europe,4,"Northern Europe",18,"Channel Island","[{zoneName:'Europe\/Jersey',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",49.25000000,-2.16666666,🇯🇪,"U+1F1EF U+1F1EA"
111,Jordan,JOR,JO,400,962,Amman,JOD,"Jordanian dinar",ا.د,.jo,الأردن,Asia,3,"Western Asia",11,Jordanian,"[{zoneName:'Asia\/Amman',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",31.00000000,36.00000000,🇯🇴,"U+1F1EF U+1F1F4"
112,Kazakhstan,KAZ,KZ,398,7,Astana,KZT,"Kazakhstani tenge",лв,.kz,Қазақстан,Asia,3,"Central Asia",10,"Kazakhstani, Kazakh","[{zoneName:'Asia\/Almaty',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'ALMT',tzName:'Alma-Ata Time[1'},{zoneName:'Asia\/Aqtau',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'AQTT',tzName:'Aqtobe Time'},{zoneName:'Asia\/Aqtobe',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'AQTT',tzName:'Aqtobe Time'},{zoneName:'Asia\/Atyrau',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'MSD+1',tzName:'Moscow Daylight Time+1'},{zoneName:'Asia\/Oral',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'ORAT',tzName:'Oral Time'},{zoneName:'Asia\/Qostanay',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'QYZST',tzName:'Qyzylorda Summer Time'},{zoneName:'Asia\/Qyzylorda',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'QYZT',tzName:'Qyzylorda Summer Time'}]",48.00000000,68.00000000,🇰🇿,"U+1F1F0 U+1F1FF"
113,Kenya,KEN,KE,404,254,Nairobi,KES,"Kenyan shilling",KSh,.ke,Kenya,Africa,1,"Eastern Africa",4,Kenyan,"[{zoneName:'Africa\/Nairobi',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",1.00000000,38.00000000,🇰🇪,"U+1F1F0 U+1F1EA"
114,Kiribati,KIR,KI,296,686,Tarawa,AUD,"Australian dollar",$,.ki,Kiribati,Oceania,5,Micronesia,21,I-Kiribati,"[{zoneName:'Pacific\/Enderbury',gmtOffset:46800,gmtOffsetName:'UTC+13:00',abbreviation:'PHOT',tzName:'Phoenix Island Time'},{zoneName:'Pacific\/Kiritimati',gmtOffset:50400,gmtOffsetName:'UTC+14:00',abbreviation:'LINT',tzName:'Line Islands Time'},{zoneName:'Pacific\/Tarawa',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'GILT',tzName:'Gilbert Island Time'}]",1.41666666,173.00000000,🇰🇮,"U+1F1F0 U+1F1EE"
248,Kosovo,XKX,XK,926,383,Pristina,EUR,Euro,€,.xk,"Republika e Kosovës",Europe,4,"Eastern Europe",15,"Kosovar, Kosovan","[{zoneName:'Europe\/Belgrade',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",42.56129090,20.34030350,🇽🇰,"U+1F1FD U+1F1F0"
117,Kuwait,KWT,KW,414,965,"Kuwait City",KWD,"Kuwaiti dinar",ك.د,.kw,الكويت,Asia,3,"Western Asia",11,Kuwaiti,"[{zoneName:'Asia\/Kuwait',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'AST',tzName:'Arabia Standard Time'}]",29.50000000,45.75000000,🇰🇼,"U+1F1F0 U+1F1FC"
118,Kyrgyzstan,KGZ,KG,417,996,Bishkek,KGS,"Kyrgyzstani som",лв,.kg,Кыргызстан,Asia,3,"Central Asia",10,"Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz","[{zoneName:'Asia\/Bishkek',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'KGT',tzName:'Kyrgyzstan Time'}]",41.00000000,75.00000000,🇰🇬,"U+1F1F0 U+1F1EC"
119,Laos,LAO,LA,418,856,Vientiane,LAK,"Lao kip",₭,.la,ສປປລາວ,Asia,3,"South-Eastern Asia",13,"Lao, Laotian","[{zoneName:'Asia\/Vientiane',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'ICT',tzName:'Indochina Time'}]",18.00000000,105.00000000,🇱🇦,"U+1F1F1 U+1F1E6"
120,Latvia,LVA,LV,428,371,Riga,EUR,Euro,€,.lv,Latvija,Europe,4,"Northern Europe",18,Latvian,"[{zoneName:'Europe\/Riga',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",57.00000000,25.00000000,🇱🇻,"U+1F1F1 U+1F1FB"
121,Lebanon,LBN,LB,422,961,Beirut,LBP,"Lebanese pound",£,.lb,لبنان,Asia,3,"Western Asia",11,Lebanese,"[{zoneName:'Asia\/Beirut',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",33.83333333,35.83333333,🇱🇧,"U+1F1F1 U+1F1E7"
122,Lesotho,LSO,LS,426,266,Maseru,LSL,"Lesotho loti",L,.ls,Lesotho,Africa,1,"Southern Africa",5,Basotho,"[{zoneName:'Africa\/Maseru',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'SAST',tzName:'South African Standard Time'}]",-29.50000000,28.50000000,🇱🇸,"U+1F1F1 U+1F1F8"
123,Liberia,LBR,LR,430,231,Monrovia,LRD,"Liberian dollar",$,.lr,Liberia,Africa,1,"Western Africa",3,Liberian,"[{zoneName:'Africa\/Monrovia',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",6.50000000,-9.50000000,🇱🇷,"U+1F1F1 U+1F1F7"
124,Libya,LBY,LY,434,218,Tripolis,LYD,"Libyan dinar",د.ل,.ly,‏ليبيا,Africa,1,"Northern Africa",1,Libyan,"[{zoneName:'Africa\/Tripoli',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",25.00000000,17.00000000,🇱🇾,"U+1F1F1 U+1F1FE"
125,Liechtenstein,LIE,LI,438,423,Vaduz,CHF,"Swiss franc",CHf,.li,Liechtenstein,Europe,4,"Western Europe",17,Liechtenstein,"[{zoneName:'Europe\/Vaduz',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",47.26666666,9.53333333,🇱🇮,"U+1F1F1 U+1F1EE"
126,Lithuania,LTU,LT,440,370,Vilnius,EUR,Euro,€,.lt,Lietuva,Europe,4,"Northern Europe",18,Lithuanian,"[{zoneName:'Europe\/Vilnius',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",56.00000000,24.00000000,🇱🇹,"U+1F1F1 U+1F1F9"
127,Luxembourg,LUX,LU,442,352,Luxembourg,EUR,Euro,€,.lu,Luxembourg,Europe,4,"Western Europe",17,"Luxembourg, Luxembourgish","[{zoneName:'Europe\/Luxembourg',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",49.75000000,6.16666666,🇱🇺,"U+1F1F1 U+1F1FA"
128,"Macau S.A.R.",MAC,MO,446,853,Macao,MOP,"Macanese pataca",$,.mo,澳門,Asia,3,"Eastern Asia",12,"Macanese, Chinese","[{zoneName:'Asia\/Macau',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'CST',tzName:'China Standard Time'}]",22.16666666,113.55000000,🇲🇴,"U+1F1F2 U+1F1F4"
130,Madagascar,MDG,MG,450,261,Antananarivo,MGA,"Malagasy ariary",Ar,.mg,Madagasikara,Africa,1,"Eastern Africa",4,Malagasy,"[{zoneName:'Indian\/Antananarivo',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",-20.00000000,47.00000000,🇲🇬,"U+1F1F2 U+1F1EC"
131,Malawi,MWI,MW,454,265,Lilongwe,MWK,"Malawian kwacha",MK,.mw,Malawi,Africa,1,"Eastern Africa",4,Malawian,"[{zoneName:'Africa\/Blantyre',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",-13.50000000,34.00000000,🇲🇼,"U+1F1F2 U+1F1FC"
132,Malaysia,MYS,MY,458,60,"Kuala Lumpur",MYR,"Malaysian ringgit",RM,.my,Malaysia,Asia,3,"South-Eastern Asia",13,Malaysian,"[{zoneName:'Asia\/Kuala_Lumpur',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'MYT',tzName:'Malaysia Time'},{zoneName:'Asia\/Kuching',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'MYT',tzName:'Malaysia Time'}]",2.50000000,112.50000000,🇲🇾,"U+1F1F2 U+1F1FE"
133,Maldives,MDV,MV,462,960,Male,MVR,"Maldivian rufiyaa",Rf,.mv,Maldives,Asia,3,"Southern Asia",14,Maldivian,"[{zoneName:'Indian\/Maldives',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'MVT',tzName:'Maldives Time'}]",3.25000000,73.00000000,🇲🇻,"U+1F1F2 U+1F1FB"
134,Mali,MLI,ML,466,223,Bamako,XOF,"West African CFA franc",CFA,.ml,Mali,Africa,1,"Western Africa",3,"Malian, Malinese","[{zoneName:'Africa\/Bamako',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",17.00000000,-4.00000000,🇲🇱,"U+1F1F2 U+1F1F1"
135,Malta,MLT,MT,470,356,Valletta,EUR,Euro,€,.mt,Malta,Europe,4,"Southern Europe",16,Maltese,"[{zoneName:'Europe\/Malta',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",35.83333333,14.58333333,🇲🇹,"U+1F1F2 U+1F1F9"
136,"Man (Isle of)",IMN,IM,833,44,"Douglas, Isle of Man",GBP,"British pound",£,.im,"Isle of Man",Europe,4,"Northern Europe",18,Manx,"[{zoneName:'Europe\/Isle_of_Man',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",54.25000000,-4.50000000,🇮🇲,"U+1F1EE U+1F1F2"
137,"Marshall Islands",MHL,MH,584,692,Majuro,USD,"United States dollar",$,.mh,M̧ajeļ,Oceania,5,Micronesia,21,Marshallese,"[{zoneName:'Pacific\/Kwajalein',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'MHT',tzName:'Marshall Islands Time'},{zoneName:'Pacific\/Majuro',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'MHT',tzName:'Marshall Islands Time'}]",9.00000000,168.00000000,🇲🇭,"U+1F1F2 U+1F1ED"
138,Martinique,MTQ,MQ,474,596,Fort-de-France,EUR,Euro,€,.mq,Martinique,Americas,2,Caribbean,7,"Martiniquais, Martinican","[{zoneName:'America\/Martinique',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",14.66666700,-61.00000000,🇲🇶,"U+1F1F2 U+1F1F6"
139,Mauritania,MRT,MR,478,222,Nouakchott,MRO,"Mauritanian ouguiya",MRU,.mr,موريتانيا,Africa,1,"Western Africa",3,Mauritanian,"[{zoneName:'Africa\/Nouakchott',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",20.00000000,-12.00000000,🇲🇷,"U+1F1F2 U+1F1F7"
140,Mauritius,MUS,MU,480,230,"Port Louis",MUR,"Mauritian rupee",₨,.mu,Maurice,Africa,1,"Eastern Africa",4,Mauritian,"[{zoneName:'Indian\/Mauritius',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'MUT',tzName:'Mauritius Time'}]",-20.28333333,57.55000000,🇲🇺,"U+1F1F2 U+1F1FA"
141,Mayotte,MYT,YT,175,262,Mamoudzou,EUR,Euro,€,.yt,Mayotte,Africa,1,"Eastern Africa",4,Mahoran,"[{zoneName:'Indian\/Mayotte',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",-12.83333333,45.16666666,🇾🇹,"U+1F1FE U+1F1F9"
142,Mexico,MEX,MX,484,52,"Ciudad de México",MXN,"Mexican peso",$,.mx,México,Americas,2,"Central America",9,Mexican,"[{zoneName:'America\/Bahia_Banderas',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Cancun',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Chihuahua',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Hermosillo',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Matamoros',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Mazatlan',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Merida',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Mexico_City',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Monterrey',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Ojinaga',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Tijuana',gmtOffset:-28800,gmtOffsetName:'UTC-08:00',abbreviation:'PST',tzName:'Pacific Standard Time (North America'}]",23.00000000,-102.00000000,🇲🇽,"U+1F1F2 U+1F1FD"
143,Micronesia,FSM,FM,583,691,Palikir,USD,"United States dollar",$,.fm,Micronesia,Oceania,5,Micronesia,21,Micronesian,"[{zoneName:'Pacific\/Chuuk',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'CHUT',tzName:'Chuuk Time'},{zoneName:'Pacific\/Kosrae',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'KOST',tzName:'Kosrae Time'},{zoneName:'Pacific\/Pohnpei',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'PONT',tzName:'Pohnpei Standard Time'}]",6.91666666,158.25000000,🇫🇲,"U+1F1EB U+1F1F2"
144,Moldova,MDA,MD,498,373,Chisinau,MDL,"Moldovan leu",L,.md,Moldova,Europe,4,"Eastern Europe",15,Moldovan,"[{zoneName:'Europe\/Chisinau',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",47.00000000,29.00000000,🇲🇩,"U+1F1F2 U+1F1E9"
145,Monaco,MCO,MC,492,377,Monaco,EUR,Euro,€,.mc,Monaco,Europe,4,"Western Europe",17,"Monegasque, Monacan","[{zoneName:'Europe\/Monaco',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",43.73333333,7.40000000,🇲🇨,"U+1F1F2 U+1F1E8"
146,Mongolia,MNG,MN,496,976,"Ulan Bator",MNT,"Mongolian tögrög",₮,.mn,"Монгол улс",Asia,3,"Eastern Asia",12,Mongolian,"[{zoneName:'Asia\/Choibalsan',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'CHOT',tzName:'Choibalsan Standard Time'},{zoneName:'Asia\/Hovd',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'HOVT',tzName:'Hovd Time'},{zoneName:'Asia\/Ulaanbaatar',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'ULAT',tzName:'Ulaanbaatar Standard Time'}]",46.00000000,105.00000000,🇲🇳,"U+1F1F2 U+1F1F3"
147,Montenegro,MNE,ME,499,382,Podgorica,EUR,Euro,€,.me,"Црна Гора",Europe,4,"Southern Europe",16,Montenegrin,"[{zoneName:'Europe\/Podgorica',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",42.50000000,19.30000000,🇲🇪,"U+1F1F2 U+1F1EA"
148,Montserrat,MSR,MS,500,1,Plymouth,XCD,"Eastern Caribbean dollar",$,.ms,Montserrat,Americas,2,Caribbean,7,Montserratian,"[{zoneName:'America\/Montserrat',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",16.75000000,-62.20000000,🇲🇸,"U+1F1F2 U+1F1F8"
149,Morocco,MAR,MA,504,212,Rabat,MAD,"Moroccan dirham",DH,.ma,المغرب,Africa,1,"Northern Africa",1,Moroccan,"[{zoneName:'Africa\/Casablanca',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WEST',tzName:'Western European Summer Time'}]",32.00000000,-5.00000000,🇲🇦,"U+1F1F2 U+1F1E6"
150,Mozambique,MOZ,MZ,508,258,Maputo,MZN,"Mozambican metical",MT,.mz,Moçambique,Africa,1,"Eastern Africa",4,Mozambican,"[{zoneName:'Africa\/Maputo',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",-18.25000000,35.00000000,🇲🇿,"U+1F1F2 U+1F1FF"
151,Myanmar,MMR,MM,104,95,"Nay Pyi Taw",MMK,"Burmese kyat",K,.mm,မြန်မာ,Asia,3,"South-Eastern Asia",13,Burmese,"[{zoneName:'Asia\/Yangon',gmtOffset:23400,gmtOffsetName:'UTC+06:30',abbreviation:'MMT',tzName:'Myanmar Standard Time'}]",22.00000000,98.00000000,🇲🇲,"U+1F1F2 U+1F1F2"
152,Namibia,NAM,NA,516,264,Windhoek,NAD,"Namibian dollar",$,.na,Namibia,Africa,1,"Southern Africa",5,Namibian,"[{zoneName:'Africa\/Windhoek',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'WAST',tzName:'West Africa Summer Time'}]",-22.00000000,17.00000000,🇳🇦,"U+1F1F3 U+1F1E6"
153,Nauru,NRU,NR,520,674,Yaren,AUD,"Australian dollar",$,.nr,Nauru,Oceania,5,Micronesia,21,Nauruan,"[{zoneName:'Pacific\/Nauru',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'NRT',tzName:'Nauru Time'}]",-0.53333333,166.91666666,🇳🇷,"U+1F1F3 U+1F1F7"
154,Nepal,NPL,NP,524,977,Kathmandu,NPR,"Nepalese rupee",₨,.np,नपल,Asia,3,"Southern Asia",14,"Nepali, Nepalese","[{zoneName:'Asia\/Kathmandu',gmtOffset:20700,gmtOffsetName:'UTC+05:45',abbreviation:'NPT',tzName:'Nepal Time'}]",28.00000000,84.00000000,🇳🇵,"U+1F1F3 U+1F1F5"
156,Netherlands,NLD,NL,528,31,Amsterdam,EUR,Euro,€,.nl,Nederland,Europe,4,"Western Europe",17,"Dutch, Netherlandic","[{zoneName:'Europe\/Amsterdam',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",52.50000000,5.75000000,🇳🇱,"U+1F1F3 U+1F1F1"
157,"New Caledonia",NCL,NC,540,687,Noumea,XPF,"CFP franc",₣,.nc,Nouvelle-Calédonie,Oceania,5,Melanesia,20,"New Caledonian","[{zoneName:'Pacific\/Noumea',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'NCT',tzName:'New Caledonia Time'}]",-21.50000000,165.50000000,🇳🇨,"U+1F1F3 U+1F1E8"
158,"New Zealand",NZL,NZ,554,64,Wellington,NZD,"New Zealand dollar",$,.nz,"New Zealand",Oceania,5,"Australia and New Zealand",19,"New Zealand, NZ","[{zoneName:'Pacific\/Auckland',gmtOffset:46800,gmtOffsetName:'UTC+13:00',abbreviation:'NZDT',tzName:'New Zealand Daylight Time'},{zoneName:'Pacific\/Chatham',gmtOffset:49500,gmtOffsetName:'UTC+13:45',abbreviation:'CHAST',tzName:'Chatham Standard Time'}]",-41.00000000,174.00000000,🇳🇿,"U+1F1F3 U+1F1FF"
159,Nicaragua,NIC,NI,558,505,Managua,NIO,"Nicaraguan córdoba",C$,.ni,Nicaragua,Americas,2,"Central America",9,Nicaraguan,"[{zoneName:'America\/Managua',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'}]",13.00000000,-85.00000000,🇳🇮,"U+1F1F3 U+1F1EE"
160,Niger,NER,NE,562,227,Niamey,XOF,"West African CFA franc",CFA,.ne,Niger,Africa,1,"Western Africa",3,Nigerien,"[{zoneName:'Africa\/Niamey',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",16.00000000,8.00000000,🇳🇪,"U+1F1F3 U+1F1EA"
161,Nigeria,NGA,NG,566,234,Abuja,NGN,"Nigerian naira",₦,.ng,Nigeria,Africa,1,"Western Africa",3,Nigerian,"[{zoneName:'Africa\/Lagos',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WAT',tzName:'West Africa Time'}]",10.00000000,8.00000000,🇳🇬,"U+1F1F3 U+1F1EC"
162,Niue,NIU,NU,570,683,Alofi,NZD,"New Zealand dollar",$,.nu,Niuē,Oceania,5,Polynesia,22,Niuean,"[{zoneName:'Pacific\/Niue',gmtOffset:-39600,gmtOffsetName:'UTC-11:00',abbreviation:'NUT',tzName:'Niue Time'}]",-19.03333333,-169.86666666,🇳🇺,"U+1F1F3 U+1F1FA"
163,"Norfolk Island",NFK,NF,574,672,Kingston,AUD,"Australian dollar",$,.nf,"Norfolk Island",Oceania,5,"Australia and New Zealand",19,"Norfolk Island","[{zoneName:'Pacific\/Norfolk',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'NFT',tzName:'Norfolk Time'}]",-29.03333333,167.95000000,🇳🇫,"U+1F1F3 U+1F1EB"
115,"North Korea",PRK,KP,408,850,Pyongyang,KPW,"North Korean Won",₩,.kp,북한,Asia,3,"Eastern Asia",12,"North Korean","[{zoneName:'Asia\/Pyongyang',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'KST',tzName:'Korea Standard Time'}]",40.00000000,127.00000000,🇰🇵,"U+1F1F0 U+1F1F5"
129,"North Macedonia",MKD,MK,807,389,Skopje,MKD,Denar,ден,.mk,"Северна Македонија",Europe,4,"Southern Europe",16,Macedonian,"[{zoneName:'Europe\/Skopje',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",41.83333333,22.00000000,🇲🇰,"U+1F1F2 U+1F1F0"
164,"Northern Mariana Islands",MNP,MP,580,1,Saipan,USD,"United States dollar",$,.mp,"Northern Mariana Islands",Oceania,5,Micronesia,21,"Northern Marianan","[{zoneName:'Pacific\/Saipan',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'ChST',tzName:'Chamorro Standard Time'}]",15.20000000,145.75000000,🇲🇵,"U+1F1F2 U+1F1F5"
165,Norway,NOR,NO,578,47,Oslo,NOK,"Norwegian krone",ko,.no,Norge,Europe,4,"Northern Europe",18,Norwegian,"[{zoneName:'Europe\/Oslo',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",62.00000000,10.00000000,🇳🇴,"U+1F1F3 U+1F1F4"
166,Oman,OMN,OM,512,968,Muscat,OMR,"Omani rial",.ع.ر,.om,عمان,Asia,3,"Western Asia",11,Omani,"[{zoneName:'Asia\/Muscat',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'GST',tzName:'Gulf Standard Time'}]",21.00000000,57.00000000,🇴🇲,"U+1F1F4 U+1F1F2"
167,Pakistan,PAK,PK,586,92,Islamabad,PKR,"Pakistani rupee",₨,.pk,پاکستان,Asia,3,"Southern Asia",14,Pakistani,"[{zoneName:'Asia\/Karachi',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'PKT',tzName:'Pakistan Standard Time'}]",30.00000000,70.00000000,🇵🇰,"U+1F1F5 U+1F1F0"
168,Palau,PLW,PW,585,680,Melekeok,USD,"United States dollar",$,.pw,Palau,Oceania,5,Micronesia,21,Palauan,"[{zoneName:'Pacific\/Palau',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'PWT',tzName:'Palau Time'}]",7.50000000,134.50000000,🇵🇼,"U+1F1F5 U+1F1FC"
169,"Palestinian Territory Occupied",PSE,PS,275,970,"East Jerusalem",ILS,"Israeli new shekel",₪,.ps,فلسطين,Asia,3,"Western Asia",11,Palestinian,"[{zoneName:'Asia\/Gaza',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'},{zoneName:'Asia\/Hebron',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",31.90000000,35.20000000,🇵🇸,"U+1F1F5 U+1F1F8"
170,Panama,PAN,PA,591,507,"Panama City",PAB,"Panamanian balboa",B/.,.pa,Panamá,Americas,2,"Central America",9,Panamanian,"[{zoneName:'America\/Panama',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'}]",9.00000000,-80.00000000,🇵🇦,"U+1F1F5 U+1F1E6"
171,"Papua New Guinea",PNG,PG,598,675,"Port Moresby",PGK,"Papua New Guinean kina",K,.pg,"Papua Niugini",Oceania,5,Melanesia,20,"Papua New Guinean, Papuan","[{zoneName:'Pacific\/Bougainville',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'BST',tzName:'Bougainville Standard Time[6'},{zoneName:'Pacific\/Port_Moresby',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'PGT',tzName:'Papua New Guinea Time'}]",-6.00000000,147.00000000,🇵🇬,"U+1F1F5 U+1F1EC"
172,Paraguay,PRY,PY,600,595,Asuncion,PYG,"Paraguayan guarani",₲,.py,Paraguay,Americas,2,"South America",8,Paraguayan,"[{zoneName:'America\/Asuncion',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'PYST',tzName:'Paraguay Summer Time'}]",-23.00000000,-58.00000000,🇵🇾,"U+1F1F5 U+1F1FE"
173,Peru,PER,PE,604,51,Lima,PEN,"Peruvian sol",S/.,.pe,Perú,Americas,2,"South America",8,Peruvian,"[{zoneName:'America\/Lima',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'PET',tzName:'Peru Time'}]",-10.00000000,-76.00000000,🇵🇪,"U+1F1F5 U+1F1EA"
174,Philippines,PHL,PH,608,63,Manila,PHP,"Philippine peso",₱,.ph,Pilipinas,Asia,3,"South-Eastern Asia",13,"Philippine, Filipino","[{zoneName:'Asia\/Manila',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'PHT',tzName:'Philippine Time'}]",13.00000000,122.00000000,🇵🇭,"U+1F1F5 U+1F1ED"
175,"Pitcairn Island",PCN,PN,612,870,Adamstown,NZD,"New Zealand dollar",$,.pn,"Pitcairn Islands",Oceania,5,Polynesia,22,"Pitcairn Island","[{zoneName:'Pacific\/Pitcairn',gmtOffset:-28800,gmtOffsetName:'UTC-08:00',abbreviation:'PST',tzName:'Pacific Standard Time (North America'}]",-25.06666666,-130.10000000,🇵🇳,"U+1F1F5 U+1F1F3"
176,Poland,POL,PL,616,48,Warsaw,PLN,"Polish złoty",zł,.pl,Polska,Europe,4,"Eastern Europe",15,Polish,"[{zoneName:'Europe\/Warsaw',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",52.00000000,20.00000000,🇵🇱,"U+1F1F5 U+1F1F1"
177,Portugal,PRT,PT,620,351,Lisbon,EUR,Euro,€,.pt,Portugal,Europe,4,"Southern Europe",16,Portuguese,"[{zoneName:'Atlantic\/Azores',gmtOffset:-3600,gmtOffsetName:'UTC-01:00',abbreviation:'AZOT',tzName:'Azores Standard Time'},{zoneName:'Atlantic\/Madeira',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'WET',tzName:'Western European Time'},{zoneName:'Europe\/Lisbon',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'WET',tzName:'Western European Time'}]",39.50000000,-8.00000000,🇵🇹,"U+1F1F5 U+1F1F9"
178,"Puerto Rico",PRI,PR,630,1,"San Juan",USD,"United States dollar",$,.pr,"Puerto Rico",Americas,2,Caribbean,7,"Puerto Rican","[{zoneName:'America\/Puerto_Rico',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",18.25000000,-66.50000000,🇵🇷,"U+1F1F5 U+1F1F7"
179,Qatar,QAT,QA,634,974,Doha,QAR,"Qatari riyal",ق.ر,.qa,قطر,Asia,3,"Western Asia",11,Qatari,"[{zoneName:'Asia\/Qatar',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'AST',tzName:'Arabia Standard Time'}]",25.50000000,51.25000000,🇶🇦,"U+1F1F6 U+1F1E6"
180,Reunion,REU,RE,638,262,Saint-Denis,EUR,Euro,€,.re,"La Réunion",Africa,1,"Eastern Africa",4,"Reunionese, Reunionnais","[{zoneName:'Indian\/Reunion',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'RET',tzName:'R\u00e9union Time'}]",-21.15000000,55.50000000,🇷🇪,"U+1F1F7 U+1F1EA"
181,Romania,ROU,RO,642,40,Bucharest,RON,"Romanian leu",lei,.ro,România,Europe,4,"Eastern Europe",15,Romanian,"[{zoneName:'Europe\/Bucharest',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",46.00000000,25.00000000,🇷🇴,"U+1F1F7 U+1F1F4"
182,Russia,RUS,RU,643,7,Moscow,RUB,"Russian ruble",₽,.ru,Россия,Europe,4,"Eastern Europe",15,Russian,"[{zoneName:'Asia\/Anadyr',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'ANAT',tzName:'Anadyr Time[4'},{zoneName:'Asia\/Barnaul',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'KRAT',tzName:'Krasnoyarsk Time'},{zoneName:'Asia\/Chita',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'YAKT',tzName:'Yakutsk Time'},{zoneName:'Asia\/Irkutsk',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'IRKT',tzName:'Irkutsk Time'},{zoneName:'Asia\/Kamchatka',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'PETT',tzName:'Kamchatka Time'},{zoneName:'Asia\/Khandyga',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'YAKT',tzName:'Yakutsk Time'},{zoneName:'Asia\/Krasnoyarsk',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'KRAT',tzName:'Krasnoyarsk Time'},{zoneName:'Asia\/Magadan',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'MAGT',tzName:'Magadan Time'},{zoneName:'Asia\/Novokuznetsk',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'KRAT',tzName:'Krasnoyarsk Time'},{zoneName:'Asia\/Novosibirsk',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'NOVT',tzName:'Novosibirsk Time'},{zoneName:'Asia\/Omsk',gmtOffset:21600,gmtOffsetName:'UTC+06:00',abbreviation:'OMST',tzName:'Omsk Time'},{zoneName:'Asia\/Sakhalin',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'SAKT',tzName:'Sakhalin Island Time'},{zoneName:'Asia\/Srednekolymsk',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'SRET',tzName:'Srednekolymsk Time'},{zoneName:'Asia\/Tomsk',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'MSD+3',tzName:'Moscow Daylight Time+3'},{zoneName:'Asia\/Ust-Nera',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'VLAT',tzName:'Vladivostok Time'},{zoneName:'Asia\/Vladivostok',gmtOffset:36000,gmtOffsetName:'UTC+10:00',abbreviation:'VLAT',tzName:'Vladivostok Time'},{zoneName:'Asia\/Yakutsk',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'YAKT',tzName:'Yakutsk Time'},{zoneName:'Asia\/Yekaterinburg',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'YEKT',tzName:'Yekaterinburg Time'},{zoneName:'Europe\/Astrakhan',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'SAMT',tzName:'Samara Time'},{zoneName:'Europe\/Kaliningrad',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'},{zoneName:'Europe\/Kirov',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'MSK',tzName:'Moscow Time'},{zoneName:'Europe\/Moscow',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'MSK',tzName:'Moscow Time'},{zoneName:'Europe\/Samara',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'SAMT',tzName:'Samara Time'},{zoneName:'Europe\/Saratov',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'MSD',tzName:'Moscow Daylight Time+4'},{zoneName:'Europe\/Ulyanovsk',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'SAMT',tzName:'Samara Time'},{zoneName:'Europe\/Volgograd',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'MSK',tzName:'Moscow Standard Time'}]",60.00000000,100.00000000,🇷🇺,"U+1F1F7 U+1F1FA"
183,Rwanda,RWA,RW,646,250,Kigali,RWF,"Rwandan franc",FRw,.rw,Rwanda,Africa,1,"Eastern Africa",4,Rwandan,"[{zoneName:'Africa\/Kigali',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",-2.00000000,30.00000000,🇷🇼,"U+1F1F7 U+1F1FC"
184,"Saint Helena",SHN,SH,654,290,Jamestown,SHP,"Saint Helena pound",£,.sh,"Saint Helena",Africa,1,"Western Africa",3,"Saint Helenian","[{zoneName:'Atlantic\/St_Helena',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",-15.95000000,-5.70000000,🇸🇭,"U+1F1F8 U+1F1ED"
185,"Saint Kitts and Nevis",KNA,KN,659,1,Basseterre,XCD,"Eastern Caribbean dollar",$,.kn,"Saint Kitts and Nevis",Americas,2,Caribbean,7,"Kittitian or Nevisian","[{zoneName:'America\/St_Kitts',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",17.33333333,-62.75000000,🇰🇳,"U+1F1F0 U+1F1F3"
186,"Saint Lucia",LCA,LC,662,1,Castries,XCD,"Eastern Caribbean dollar",$,.lc,"Saint Lucia",Americas,2,Caribbean,7,"Saint Lucian","[{zoneName:'America\/St_Lucia',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",13.88333333,-60.96666666,🇱🇨,"U+1F1F1 U+1F1E8"
187,"Saint Pierre and Miquelon",SPM,PM,666,508,Saint-Pierre,EUR,Euro,€,.pm,Saint-Pierre-et-Miquelon,Americas,2,"Northern America",6,"Saint-Pierrais or Miquelonnais","[{zoneName:'America\/Miquelon',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'PMDT',tzName:'Pierre & Miquelon Daylight Time'}]",46.83333333,-56.33333333,🇵🇲,"U+1F1F5 U+1F1F2"
188,"Saint Vincent and the Grenadines",VCT,VC,670,1,Kingstown,XCD,"Eastern Caribbean dollar",$,.vc,"Saint Vincent and the Grenadines",Americas,2,Caribbean,7,"Saint Vincentian, Vincentian","[{zoneName:'America\/St_Vincent',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",13.25000000,-61.20000000,🇻🇨,"U+1F1FB U+1F1E8"
189,Saint-Barthelemy,BLM,BL,652,590,Gustavia,EUR,Euro,€,.bl,Saint-Barthélemy,Americas,2,Caribbean,7,Barthelemois,"[{zoneName:'America\/St_Barthelemy',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",18.50000000,-63.41666666,🇧🇱,"U+1F1E7 U+1F1F1"
190,"Saint-Martin (French part)",MAF,MF,663,590,Marigot,EUR,Euro,€,.mf,Saint-Martin,Americas,2,Caribbean,7,Saint-Martinoise,"[{zoneName:'America\/Marigot',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",18.08333333,-63.95000000,🇲🇫,"U+1F1F2 U+1F1EB"
191,Samoa,WSM,WS,882,685,Apia,WST,"Samoan tālā",SAT,.ws,Samoa,Oceania,5,Polynesia,22,Samoan,"[{zoneName:'Pacific\/Apia',gmtOffset:50400,gmtOffsetName:'UTC+14:00',abbreviation:'WST',tzName:'West Samoa Time'}]",-13.58333333,-172.33333333,🇼🇸,"U+1F1FC U+1F1F8"
192,"San Marino",SMR,SM,674,378,"San Marino",EUR,Euro,€,.sm,"San Marino",Europe,4,"Southern Europe",16,Sammarinese,"[{zoneName:'Europe\/San_Marino',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",43.76666666,12.41666666,🇸🇲,"U+1F1F8 U+1F1F2"
193,"Sao Tome and Principe",STP,ST,678,239,"Sao Tome",STD,Dobra,Db,.st,"São Tomé e Príncipe",Africa,1,"Middle Africa",2,"Sao Tomean","[{zoneName:'Africa\/Sao_Tome',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",1.00000000,7.00000000,🇸🇹,"U+1F1F8 U+1F1F9"
194,"Saudi Arabia",SAU,SA,682,966,Riyadh,SAR,"Saudi riyal",﷼,.sa,"المملكة العربية السعودية",Asia,3,"Western Asia",11,"Saudi, Saudi Arabian","[{zoneName:'Asia\/Riyadh',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'AST',tzName:'Arabia Standard Time'}]",25.00000000,45.00000000,🇸🇦,"U+1F1F8 U+1F1E6"
195,Senegal,SEN,SN,686,221,Dakar,XOF,"West African CFA franc",CFA,.sn,Sénégal,Africa,1,"Western Africa",3,Senegalese,"[{zoneName:'Africa\/Dakar',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",14.00000000,-14.00000000,🇸🇳,"U+1F1F8 U+1F1F3"
196,Serbia,SRB,RS,688,381,Belgrade,RSD,"Serbian dinar",din,.rs,Србија,Europe,4,"Southern Europe",16,Serbian,"[{zoneName:'Europe\/Belgrade',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",44.00000000,21.00000000,🇷🇸,"U+1F1F7 U+1F1F8"
197,Seychelles,SYC,SC,690,248,Victoria,SCR,"Seychellois rupee",SRe,.sc,Seychelles,Africa,1,"Eastern Africa",4,Seychellois,"[{zoneName:'Indian\/Mahe',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'SCT',tzName:'Seychelles Time'}]",-4.58333333,55.66666666,🇸🇨,"U+1F1F8 U+1F1E8"
198,"Sierra Leone",SLE,SL,694,232,Freetown,SLL,"Sierra Leonean leone",Le,.sl,"Sierra Leone",Africa,1,"Western Africa",3,"Sierra Leonean","[{zoneName:'Africa\/Freetown',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",8.50000000,-11.50000000,🇸🇱,"U+1F1F8 U+1F1F1"
199,Singapore,SGP,SG,702,65,Singapur,SGD,"Singapore dollar",$,.sg,Singapore,Asia,3,"South-Eastern Asia",13,Singaporean,"[{zoneName:'Asia\/Singapore',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'SGT',tzName:'Singapore Time'}]",1.36666666,103.80000000,🇸🇬,"U+1F1F8 U+1F1EC"
250,"Sint Maarten (Dutch part)",SXM,SX,534,1721,Philipsburg,ANG,"Netherlands Antillean guilder",ƒ,.sx,"Sint Maarten",Americas,2,Caribbean,7,"Sint Maarten","[{zoneName:'America\/Anguilla',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",18.03333300,-63.05000000,🇸🇽,"U+1F1F8 U+1F1FD"
200,Slovakia,SVK,SK,703,421,Bratislava,EUR,Euro,€,.sk,Slovensko,Europe,4,"Eastern Europe",15,Slovak,"[{zoneName:'Europe\/Bratislava',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",48.66666666,19.50000000,🇸🇰,"U+1F1F8 U+1F1F0"
201,Slovenia,SVN,SI,705,386,Ljubljana,EUR,Euro,€,.si,Slovenija,Europe,4,"Southern Europe",16,"Slovenian, Slovene","[{zoneName:'Europe\/Ljubljana',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",46.11666666,14.81666666,🇸🇮,"U+1F1F8 U+1F1EE"
202,"Solomon Islands",SLB,SB,090,677,Honiara,SBD,"Solomon Islands dollar",Si$,.sb,"Solomon Islands",Oceania,5,Melanesia,20,"Solomon Island","[{zoneName:'Pacific\/Guadalcanal',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'SBT',tzName:'Solomon Islands Time'}]",-8.00000000,159.00000000,🇸🇧,"U+1F1F8 U+1F1E7"
203,Somalia,SOM,SO,706,252,Mogadishu,SOS,"Somali shilling",Sh.so.,.so,Soomaaliya,Africa,1,"Eastern Africa",4,"Somali, Somalian","[{zoneName:'Africa\/Mogadishu',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",10.00000000,49.00000000,🇸🇴,"U+1F1F8 U+1F1F4"
204,"South Africa",ZAF,ZA,710,27,Pretoria,ZAR,"South African rand",R,.za,"South Africa",Africa,1,"Southern Africa",5,"South African","[{zoneName:'Africa\/Johannesburg',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'SAST',tzName:'South African Standard Time'}]",-29.00000000,24.00000000,🇿🇦,"U+1F1FF U+1F1E6"
205,"South Georgia",SGS,GS,239,500,Grytviken,GBP,"British pound",£,.gs,"South Georgia",Americas,2,"South America",8,"South Georgia or South Sandwich Islands","[{zoneName:'Atlantic\/South_Georgia',gmtOffset:-7200,gmtOffsetName:'UTC-02:00',abbreviation:'GST',tzName:'South Georgia and the South Sandwich Islands Time'}]",-54.50000000,-37.00000000,🇬🇸,"U+1F1EC U+1F1F8"
116,"South Korea",KOR,KR,410,82,Seoul,KRW,Won,₩,.kr,대한민국,Asia,3,"Eastern Asia",12,"South Korean","[{zoneName:'Asia\/Seoul',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'KST',tzName:'Korea Standard Time'}]",37.00000000,127.50000000,🇰🇷,"U+1F1F0 U+1F1F7"
206,"South Sudan",SSD,SS,728,211,Juba,SSP,"South Sudanese pound",£,.ss,"South Sudan",Africa,1,"Middle Africa",2,"South Sudanese","[{zoneName:'Africa\/Juba',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",7.00000000,30.00000000,🇸🇸,"U+1F1F8 U+1F1F8"
207,Spain,ESP,ES,724,34,Madrid,EUR,Euro,€,.es,España,Europe,4,"Southern Europe",16,Spanish,"[{zoneName:'Africa\/Ceuta',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'},{zoneName:'Atlantic\/Canary',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'WET',tzName:'Western European Time'},{zoneName:'Europe\/Madrid',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",40.00000000,-4.00000000,🇪🇸,"U+1F1EA U+1F1F8"
208,"Sri Lanka",LKA,LK,144,94,Colombo,LKR,"Sri Lankan rupee",Rs,.lk,"śrī laṃkāva",Asia,3,"Southern Asia",14,"Sri Lankan","[{zoneName:'Asia\/Colombo',gmtOffset:19800,gmtOffsetName:'UTC+05:30',abbreviation:'IST',tzName:'Indian Standard Time'}]",7.00000000,81.00000000,🇱🇰,"U+1F1F1 U+1F1F0"
209,Sudan,SDN,SD,729,249,Khartoum,SDG,"Sudanese pound",.س.ج,.sd,السودان,Africa,1,"Northern Africa",1,Sudanese,"[{zoneName:'Africa\/Khartoum',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EAT',tzName:'Eastern African Time'}]",15.00000000,30.00000000,🇸🇩,"U+1F1F8 U+1F1E9"
210,Suriname,SUR,SR,740,597,Paramaribo,SRD,"Surinamese dollar",$,.sr,Suriname,Americas,2,"South America",8,Surinamese,"[{zoneName:'America\/Paramaribo',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'SRT',tzName:'Suriname Time'}]",4.00000000,-56.00000000,🇸🇷,"U+1F1F8 U+1F1F7"
211,"Svalbard and Jan Mayen Islands",SJM,SJ,744,47,Longyearbyen,NOK,"Norwegian krone",ko,.sj,"Svalbard og Jan Mayen",Europe,4,"Northern Europe",18,Svalbard,"[{zoneName:'Arctic\/Longyearbyen',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",78.00000000,20.00000000,🇸🇯,"U+1F1F8 U+1F1EF"
213,Sweden,SWE,SE,752,46,Stockholm,SEK,"Swedish krona",ko,.se,Sverige,Europe,4,"Northern Europe",18,Swedish,"[{zoneName:'Europe\/Stockholm',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",62.00000000,15.00000000,🇸🇪,"U+1F1F8 U+1F1EA"
214,Switzerland,CHE,CH,756,41,Bern,CHF,"Swiss franc",CHf,.ch,Schweiz,Europe,4,"Western Europe",17,Swiss,"[{zoneName:'Europe\/Zurich',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",47.00000000,8.00000000,🇨🇭,"U+1F1E8 U+1F1ED"
215,Syria,SYR,SY,760,963,Damascus,SYP,"Syrian pound",LS,.sy,سوريا,Asia,3,"Western Asia",11,Syrian,"[{zoneName:'Asia\/Damascus',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",35.00000000,38.00000000,🇸🇾,"U+1F1F8 U+1F1FE"
216,Taiwan,TWN,TW,158,886,Taipei,TWD,"New Taiwan dollar",$,.tw,臺灣,Asia,3,"Eastern Asia",12,"Chinese, Taiwanese","[{zoneName:'Asia\/Taipei',gmtOffset:28800,gmtOffsetName:'UTC+08:00',abbreviation:'CST',tzName:'China Standard Time'}]",23.50000000,121.00000000,🇹🇼,"U+1F1F9 U+1F1FC"
217,Tajikistan,TJK,TJ,762,992,Dushanbe,TJS,"Tajikistani somoni",SM,.tj,Тоҷикистон,Asia,3,"Central Asia",10,Tajikistani,"[{zoneName:'Asia\/Dushanbe',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'TJT',tzName:'Tajikistan Time'}]",39.00000000,71.00000000,🇹🇯,"U+1F1F9 U+1F1EF"
218,Tanzania,TZA,TZ,834,255,Dodoma,TZS,"Tanzanian shilling",TSh,.tz,Tanzania,Africa,1,"Eastern Africa",4,Tanzanian,"[{zoneName:'Africa\/Dar_es_Salaam',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",-6.00000000,35.00000000,🇹🇿,"U+1F1F9 U+1F1FF"
219,Thailand,THA,TH,764,66,Bangkok,THB,"Thai baht",฿,.th,ประเทศไทย,Asia,3,"South-Eastern Asia",13,Thai,"[{zoneName:'Asia\/Bangkok',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'ICT',tzName:'Indochina Time'}]",15.00000000,100.00000000,🇹🇭,"U+1F1F9 U+1F1ED"
17,"The Bahamas",BHS,BS,044,1,Nassau,BSD,"Bahamian dollar",B$,.bs,Bahamas,Americas,2,Caribbean,7,Bahamian,"[{zoneName:'America\/Nassau',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America)'}]",24.25000000,-76.00000000,🇧🇸,"U+1F1E7 U+1F1F8"
80,"The Gambia ",GMB,GM,270,220,Banjul,GMD,"Gambian dalasi",D,.gm,Gambia,Africa,1,"Western Africa",3,Gambian,"[{zoneName:'Africa\/Banjul',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",13.46666666,-16.56666666,🇬🇲,"U+1F1EC U+1F1F2"
63,Timor-Leste,TLS,TL,626,670,Dili,USD,"United States dollar",$,.tl,Timor-Leste,Asia,3,"South-Eastern Asia",13,Timorese,"[{zoneName:'Asia\/Dili',gmtOffset:32400,gmtOffsetName:'UTC+09:00',abbreviation:'TLT',tzName:'Timor Leste Time'}]",-8.83333333,125.91666666,🇹🇱,"U+1F1F9 U+1F1F1"
220,Togo,TGO,TG,768,228,Lome,XOF,"West African CFA franc",CFA,.tg,Togo,Africa,1,"Western Africa",3,Togolese,"[{zoneName:'Africa\/Lome',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",8.00000000,1.16666666,🇹🇬,"U+1F1F9 U+1F1EC"
221,Tokelau,TKL,TK,772,690,,NZD,"New Zealand dollar",$,.tk,Tokelau,Oceania,5,Polynesia,22,Tokelauan,"[{zoneName:'Pacific\/Fakaofo',gmtOffset:46800,gmtOffsetName:'UTC+13:00',abbreviation:'TKT',tzName:'Tokelau Time'}]",-9.00000000,-172.00000000,🇹🇰,"U+1F1F9 U+1F1F0"
222,Tonga,TON,TO,776,676,Nuku'alofa,TOP,"Tongan paʻanga",$,.to,Tonga,Oceania,5,Polynesia,22,Tongan,"[{zoneName:'Pacific\/Tongatapu',gmtOffset:46800,gmtOffsetName:'UTC+13:00',abbreviation:'TOT',tzName:'Tonga Time'}]",-20.00000000,-175.00000000,🇹🇴,"U+1F1F9 U+1F1F4"
223,"Trinidad and Tobago",TTO,TT,780,1,"Port of Spain",TTD,"Trinidad and Tobago dollar",$,.tt,"Trinidad and Tobago",Americas,2,Caribbean,7,"Trinidadian or Tobagonian","[{zoneName:'America\/Port_of_Spain',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",11.00000000,-61.00000000,🇹🇹,"U+1F1F9 U+1F1F9"
224,Tunisia,TUN,TN,788,216,Tunis,TND,"Tunisian dinar",ت.د,.tn,تونس,Africa,1,"Northern Africa",1,Tunisian,"[{zoneName:'Africa\/Tunis',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",34.00000000,9.00000000,🇹🇳,"U+1F1F9 U+1F1F3"
225,Turkey,TUR,TR,792,90,Ankara,TRY,"Turkish lira",₺,.tr,Türkiye,Asia,3,"Western Asia",11,Turkish,"[{zoneName:'Europe\/Istanbul',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EET',tzName:'Eastern European Time'}]",39.00000000,35.00000000,🇹🇷,"U+1F1F9 U+1F1F7"
226,Turkmenistan,TKM,TM,795,993,Ashgabat,TMT,"Turkmenistan manat",T,.tm,Türkmenistan,Asia,3,"Central Asia",10,Turkmen,"[{zoneName:'Asia\/Ashgabat',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'TMT',tzName:'Turkmenistan Time'}]",40.00000000,60.00000000,🇹🇲,"U+1F1F9 U+1F1F2"
227,"Turks and Caicos Islands",TCA,TC,796,1,"Cockburn Town",USD,"United States dollar",$,.tc,"Turks and Caicos Islands",Americas,2,Caribbean,7,"Turks and Caicos Island","[{zoneName:'America\/Grand_Turk',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'}]",21.75000000,-71.58333333,🇹🇨,"U+1F1F9 U+1F1E8"
228,Tuvalu,TUV,TV,798,688,Funafuti,AUD,"Australian dollar",$,.tv,Tuvalu,Oceania,5,Polynesia,22,Tuvaluan,"[{zoneName:'Pacific\/Funafuti',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'TVT',tzName:'Tuvalu Time'}]",-8.00000000,178.00000000,🇹🇻,"U+1F1F9 U+1F1FB"
229,Uganda,UGA,UG,800,256,Kampala,UGX,"Ugandan shilling",USh,.ug,Uganda,Africa,1,"Eastern Africa",4,Ugandan,"[{zoneName:'Africa\/Kampala',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'EAT',tzName:'East Africa Time'}]",1.00000000,32.00000000,🇺🇬,"U+1F1FA U+1F1EC"
230,Ukraine,UKR,UA,804,380,Kyiv,UAH,"Ukrainian hryvnia",₴,.ua,Україна,Europe,4,"Eastern Europe",15,Ukrainian,"[{zoneName:'Europe\/Kiev',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'},{zoneName:'Europe\/Simferopol',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'MSK',tzName:'Moscow Time'},{zoneName:'Europe\/Uzhgorod',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'},{zoneName:'Europe\/Zaporozhye',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'EET',tzName:'Eastern European Time'}]",49.00000000,32.00000000,🇺🇦,"U+1F1FA U+1F1E6"
231,"United Arab Emirates",ARE,AE,784,971,"Abu Dhabi",AED,"United Arab Emirates dirham",إ.د,.ae,"دولة الإمارات العربية المتحدة",Asia,3,"Western Asia",11,"Emirati, Emirian, Emiri","[{zoneName:'Asia\/Dubai',gmtOffset:14400,gmtOffsetName:'UTC+04:00',abbreviation:'GST',tzName:'Gulf Standard Time'}]",24.00000000,54.00000000,🇦🇪,"U+1F1E6 U+1F1EA"
232,"United Kingdom",GBR,GB,826,44,London,GBP,"British pound",£,.uk,"United Kingdom",Europe,4,"Northern Europe",18,"British, UK","[{zoneName:'Europe\/London',gmtOffset:0,gmtOffsetName:'UTC\u00b100',abbreviation:'GMT',tzName:'Greenwich Mean Time'}]",54.00000000,-2.00000000,🇬🇧,"U+1F1EC U+1F1E7"
233,"United States",USA,US,840,1,Washington,USD,"United States dollar",$,.us,"United States",Americas,2,"Northern America",6,American,"[{zoneName:'America\/Adak',gmtOffset:-36000,gmtOffsetName:'UTC-10:00',abbreviation:'HST',tzName:'Hawaii\u2013Aleutian Standard Time'},{zoneName:'America\/Anchorage',gmtOffset:-32400,gmtOffsetName:'UTC-09:00',abbreviation:'AKST',tzName:'Alaska Standard Time'},{zoneName:'America\/Boise',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Chicago',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Denver',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Detroit',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Indiana\/Indianapolis',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Indiana\/Knox',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Indiana\/Marengo',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Indiana\/Petersburg',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Indiana\/Tell_City',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Indiana\/Vevay',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Indiana\/Vincennes',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Indiana\/Winamac',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Juneau',gmtOffset:-32400,gmtOffsetName:'UTC-09:00',abbreviation:'AKST',tzName:'Alaska Standard Time'},{zoneName:'America\/Kentucky\/Louisville',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Kentucky\/Monticello',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Los_Angeles',gmtOffset:-28800,gmtOffsetName:'UTC-08:00',abbreviation:'PST',tzName:'Pacific Standard Time (North America'},{zoneName:'America\/Menominee',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Metlakatla',gmtOffset:-32400,gmtOffsetName:'UTC-09:00',abbreviation:'AKST',tzName:'Alaska Standard Time'},{zoneName:'America\/New_York',gmtOffset:-18000,gmtOffsetName:'UTC-05:00',abbreviation:'EST',tzName:'Eastern Standard Time (North America'},{zoneName:'America\/Nome',gmtOffset:-32400,gmtOffsetName:'UTC-09:00',abbreviation:'AKST',tzName:'Alaska Standard Time'},{zoneName:'America\/North_Dakota\/Beulah',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/North_Dakota\/Center',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/North_Dakota\/New_Salem',gmtOffset:-21600,gmtOffsetName:'UTC-06:00',abbreviation:'CST',tzName:'Central Standard Time (North America'},{zoneName:'America\/Phoenix',gmtOffset:-25200,gmtOffsetName:'UTC-07:00',abbreviation:'MST',tzName:'Mountain Standard Time (North America'},{zoneName:'America\/Sitka',gmtOffset:-32400,gmtOffsetName:'UTC-09:00',abbreviation:'AKST',tzName:'Alaska Standard Time'},{zoneName:'America\/Yakutat',gmtOffset:-32400,gmtOffsetName:'UTC-09:00',abbreviation:'AKST',tzName:'Alaska Standard Time'},{zoneName:'Pacific\/Honolulu',gmtOffset:-36000,gmtOffsetName:'UTC-10:00',abbreviation:'HST',tzName:'Hawaii\u2013Aleutian Standard Time'}]",38.00000000,-97.00000000,🇺🇸,"U+1F1FA U+1F1F8"
234,"United States Minor Outlying Islands",UMI,UM,581,1,,USD,"United States dollar",$,.us,"United States Minor Outlying Islands",Americas,2,"Northern America",6,American,"[{zoneName:'Pacific\/Midway',gmtOffset:-39600,gmtOffsetName:'UTC-11:00',abbreviation:'SST',tzName:'Samoa Standard Time'},{zoneName:'Pacific\/Wake',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'WAKT',tzName:'Wake Island Time'}]",0.00000000,0.00000000,🇺🇲,"U+1F1FA U+1F1F2"
235,Uruguay,URY,UY,858,598,Montevideo,UYU,"Uruguayan peso",$,.uy,Uruguay,Americas,2,"South America",8,Uruguayan,"[{zoneName:'America\/Montevideo',gmtOffset:-10800,gmtOffsetName:'UTC-03:00',abbreviation:'UYT',tzName:'Uruguay Standard Time'}]",-33.00000000,-56.00000000,🇺🇾,"U+1F1FA U+1F1FE"
236,Uzbekistan,UZB,UZ,860,998,Tashkent,UZS,"Uzbekistani soʻm",лв,.uz,O‘zbekiston,Asia,3,"Central Asia",10,"Uzbekistani, Uzbek","[{zoneName:'Asia\/Samarkand',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'UZT',tzName:'Uzbekistan Time'},{zoneName:'Asia\/Tashkent',gmtOffset:18000,gmtOffsetName:'UTC+05:00',abbreviation:'UZT',tzName:'Uzbekistan Time'}]",41.00000000,64.00000000,🇺🇿,"U+1F1FA U+1F1FF"
237,Vanuatu,VUT,VU,548,678,"Port Vila",VUV,"Vanuatu vatu",VT,.vu,Vanuatu,Oceania,5,Melanesia,20,"Ni-Vanuatu, Vanuatuan","[{zoneName:'Pacific\/Efate',gmtOffset:39600,gmtOffsetName:'UTC+11:00',abbreviation:'VUT',tzName:'Vanuatu Time'}]",-16.00000000,167.00000000,🇻🇺,"U+1F1FB U+1F1FA"
238,"Vatican City State (Holy See)",VAT,VA,336,379,"Vatican City",EUR,Euro,€,.va,Vaticano,Europe,4,"Southern Europe",16,Vatican,"[{zoneName:'Europe\/Vatican',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'CET',tzName:'Central European Time'}]",41.90000000,12.45000000,🇻🇦,"U+1F1FB U+1F1E6"
239,Venezuela,VEN,VE,862,58,Caracas,VES,Bolívar,Bs,.ve,Venezuela,Americas,2,"South America",8,Venezuelan,"[{zoneName:'America\/Caracas',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'VET',tzName:'Venezuelan Standard Time'}]",8.00000000,-66.00000000,🇻🇪,"U+1F1FB U+1F1EA"
240,Vietnam,VNM,VN,704,84,Hanoi,VND,"Vietnamese đồng",₫,.vn,"Việt Nam",Asia,3,"South-Eastern Asia",13,Vietnamese,"[{zoneName:'Asia\/Ho_Chi_Minh',gmtOffset:25200,gmtOffsetName:'UTC+07:00',abbreviation:'ICT',tzName:'Indochina Time'}]",16.16666666,107.83333333,🇻🇳,"U+1F1FB U+1F1F3"
241,"Virgin Islands (British)",VGB,VG,092,1,"Road Town",USD,"United States dollar",$,.vg,"British Virgin Islands",Americas,2,Caribbean,7,"British Virgin Island","[{zoneName:'America\/Tortola',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",18.43138300,-64.62305000,🇻🇬,"U+1F1FB U+1F1EC"
242,"Virgin Islands (US)",VIR,VI,850,1,"Charlotte Amalie",USD,"United States dollar",$,.vi,"United States Virgin Islands",Americas,2,Caribbean,7,"U.S. Virgin Island","[{zoneName:'America\/St_Thomas',gmtOffset:-14400,gmtOffsetName:'UTC-04:00',abbreviation:'AST',tzName:'Atlantic Standard Time'}]",18.34000000,-64.93000000,🇻🇮,"U+1F1FB U+1F1EE"
243,"Wallis and Futuna Islands",WLF,WF,876,681,"Mata Utu",XPF,"CFP franc",₣,.wf,"Wallis et Futuna",Oceania,5,Polynesia,22,"Wallis and Futuna, Wallisian or Futunan","[{zoneName:'Pacific\/Wallis',gmtOffset:43200,gmtOffsetName:'UTC+12:00',abbreviation:'WFT',tzName:'Wallis & Futuna Time'}]",-13.30000000,-176.20000000,🇼🇫,"U+1F1FC U+1F1EB"
244,"Western Sahara",ESH,EH,732,212,El-Aaiun,MAD,"Moroccan dirham",MAD,.eh,"الصحراء الغربية",Africa,1,"Northern Africa",1,"Sahrawi, Sahrawian, Sahraouian","[{zoneName:'Africa\/El_Aaiun',gmtOffset:3600,gmtOffsetName:'UTC+01:00',abbreviation:'WEST',tzName:'Western European Summer Time'}]",24.50000000,-13.00000000,🇪🇭,"U+1F1EA U+1F1ED"
245,Yemen,YEM,YE,887,967,Sanaa,YER,"Yemeni rial",﷼,.ye,اليَمَن,Asia,3,"Western Asia",11,Yemeni,"[{zoneName:'Asia\/Aden',gmtOffset:10800,gmtOffsetName:'UTC+03:00',abbreviation:'AST',tzName:'Arabia Standard Time'}]",15.00000000,48.00000000,🇾🇪,"U+1F1FE U+1F1EA"
246,Zambia,ZMB,ZM,894,260,Lusaka,ZMW,"Zambian kwacha",ZK,.zm,Zambia,Africa,1,"Southern Africa",5,Zambian,"[{zoneName:'Africa\/Lusaka',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",-15.00000000,30.00000000,🇿🇲,"U+1F1FF U+1F1F2"
247,Zimbabwe,ZWE,ZW,716,263,Harare,ZWL,"Zimbabwe Dollar",$,.zw,Zimbabwe,Africa,1,"Eastern Africa",4,Zimbabwean,"[{zoneName:'Africa\/Harare',gmtOffset:7200,gmtOffsetName:'UTC+02:00',abbreviation:'CAT',tzName:'Central Africa Time'}]",-20.00000000,30.00000000,🇿🇼,"U+1F1FF U+1F1FC"
