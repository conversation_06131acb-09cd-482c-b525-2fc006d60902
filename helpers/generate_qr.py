import qrcode
import os

def generate_qr_code(url, filename):
    # Define the directory where the QR code will be saved
    qr_dir = os.path.join("media", "profile_qr")
    
    # Check if the directory exists, if not, create it
    if not os.path.exists(qr_dir):
        os.makedirs(qr_dir)

    # Full path to save the QR code
    qr_path = os.path.join(qr_dir, f"{filename}.png")
    
    # Generate the QR code
    qr = qrcode.QRCode(
        version=1,  # Version of the QR code, controls size
        error_correction=qrcode.constants.ERROR_CORRECT_L,  # Error correction level
        box_size=10,  # Size of the QR code boxes
        border=4,  # Border size
    )
    qr.add_data(url)  # Add the URL to the QR code
    qr.make(fit=True)  # Optimize the QR code size based on input

    # Create an image from the QR code instance
    img = qr.make_image(fill='black', back_color='white')
    
    # Save the QR code image to the defined path
    img.save(qr_path)
    
    # Return the full path of the saved QR code
    return qr_path

# print(generate_qr_code('https://staging.yooii.com','raj'))