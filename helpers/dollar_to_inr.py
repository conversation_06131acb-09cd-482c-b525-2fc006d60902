# from forex_python.converter import CurrencyRates

# # Initialize the converter
# c = CurrencyRates()

# def fun_dollar_to_inr(amount_in_usd):
#     inr_amount = c.convert('USD', 'INR', amount_in_usd)
#     return round(inr_amount)


# print(fun_dollar_to_inr(100))

import requests

def fun_dollar_to_inr(amount_in_usd):
    url = 'https://api.exchangerate-api.com/v4/latest/USD'
    try:
        response = requests.get(url, timeout=5)
        data = response.json()
        inr_rate = data['rates']['INR']
        return round(amount_in_usd * inr_rate)
    except Exception as e:
        print("Error fetching conversion rate:", e)
        return None
