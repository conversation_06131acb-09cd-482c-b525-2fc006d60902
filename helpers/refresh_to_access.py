import json
import requests

def get_access_token_from_refresh_token(json_file_path,refresh_token):
    # Read the JSON file
    with open(json_file_path, 'r') as file:
        credentials = json.load(file)

    # Extract the refresh token, client_id, and client_secret from the JSON file
    client_id = credentials['client_id']
    client_secret = credentials['client_secret']
    # refresh_token = credentials['refresh_token']

    # Define the token endpoint
    token_url = "https://oauth2.googleapis.com/token"

    # Prepare the payload for the token request
    payload = {
        "client_id": client_id,
        "client_secret": client_secret,
        "refresh_token": refresh_token,
        "grant_type": "refresh_token"
    }

    # Make the POST request to refresh the token
    response = requests.post(token_url, data=payload)

    # If the response is successful, parse the access token
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data["access_token"]
        expires_in = token_data["expires_in"]
        return access_token
    else:
        # If the request fails, raise an exception with the error message
        return 0

# access_token, expires_in = get_access_token_from_refresh_token(json_file_path)

