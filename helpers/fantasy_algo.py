def distribute_prize_pool(price_pool, max_reward_rank):
    """
    Distributes prize pool across ranks with strictly descending amounts.
    Ensures ALL ranks get meaningful rewards (minimum amount guaranteed).
    
    Args:
        price_pool (int/float): Total prize pool amount
        max_reward_rank (int): Maximum rank that gets rewards
    
    Returns:
        list: List of dictionaries with rank as key and amount as value
        
    Algorithm Logic:
    - Sets minimum reward for last rank (0.1% of pool or at least 10)
    - Calculates optimal decay factor to distribute remaining pool
    - Guarantees strictly descending order with meaningful amounts
    """
    
    if max_reward_rank <= 0 or price_pool <= 0:
        return []
    
    if max_reward_rank == 1:
        return [{'1': int(price_pool)}]
    
    # Set minimum reward for the last rank (0.1% of pool, minimum 10)
    min_reward = max(10, int(price_pool * 0.001))
    
    # Calculate optimal decay to ensure good distribution
    # For large number of ranks, use gentler decay
    if max_reward_rank <= 10:
        decay_factor = 0.75
    elif max_reward_rank <= 50:
        decay_factor = 0.85
    else:
        decay_factor = 0.90  # Very gentle for large rank counts
    
    # Reserve pool for minimum rewards
    reserved_pool = min_reward * max_reward_rank
    remaining_pool = price_pool - reserved_pool
    
    if remaining_pool <= 0:
        # If pool is too small, distribute equally
        equal_amount = price_pool // max_reward_rank
        remainder = price_pool % max_reward_rank
        
        rewards = []
        for rank in range(1, max_reward_rank + 1):
            amount = equal_amount + (1 if rank <= remainder else 0)
            # Make it slightly descending
            amount = equal_amount + max_reward_rank - rank + (1 if rank <= remainder else 0)
            rewards.append({str(rank): amount})
        return rewards
    
    # Calculate exponential weights for the remaining pool
    weights = []
    for rank in range(max_reward_rank):
        weight = decay_factor ** rank
        weights.append(weight)
    
    total_weight = sum(weights)
    
    # Calculate amounts: minimum + proportional share of remaining pool
    amounts = []
    for rank in range(max_reward_rank):
        proportional_share = int(remaining_pool * (weights[rank] / total_weight))
        total_amount = min_reward + proportional_share
        amounts.append(total_amount)
    
    # Ensure strict descending order
    for i in range(1, len(amounts)):
        if amounts[i] >= amounts[i-1]:
            # Make current rank meaningfully less than previous (at least 2% less or 1 less minimum)
            reduction = max(1, int(amounts[i-1] * 0.02))
            amounts[i] = amounts[i-1] - reduction
    
    # Ensure no amount goes below minimum
    for i in range(len(amounts)):
        amounts[i] = max(min_reward, amounts[i])
    
    # Final adjustment to match total pool exactly
    current_total = sum(amounts)
    difference = price_pool - current_total
    
    # Distribute difference proportionally to top ranks
    if difference != 0:
        # Add difference to rank 1 primarily, but ensure descending order maintained
        amounts[0] += difference
        
        # Re-ensure descending order after adjustment
        for i in range(1, len(amounts)):
            if amounts[i] >= amounts[i-1]:
                needed_reduction = amounts[i] - amounts[i-1] + max(1, int(amounts[i-1] * 0.02))
                amounts[i] = max(min_reward, amounts[i] - needed_reduction)
    
    # Create final result
    rewards = []
    for rank in range(1, max_reward_rank + 1):
        rewards.append({str(rank): amounts[rank-1]})
    
    return rewards

result = distribute_prize_pool(100000, 3)
print("Result:", result)