import base64
import magic
import os
from flask import current_app as app

def save_base64_file(data,user_id,time,upload_folder=None):
    try:
        # Step 1: Extract the MIME type and base64 data
        header, base64_data = data.split(',', 1)
        mime_type = header.split(';')[0].split(':')[1]

        # Step 2: Decode the base64 string to get binary data
        file_content = base64.b64decode(base64_data)

        # Step 3: Determine the file extension
        file_extension = mime_type.split('/')[-1]  # e.g., "jpeg"

        # Alternatively, use python-magic to determine the file type if necessary
        def determine_file_type(content):
            mime = magic.Magic(mime=True)
            return mime.from_buffer(content)  # e.g., "image/jpeg"

        detected_mime_type = determine_file_type(file_content)
        detected_extension = detected_mime_type.split('/')[-1]  # e.g., "jpeg"

        # Use detected_extension if mime_type wasn't initially available
        file_extension = file_extension or detected_extension

        # Step 4: Save the file
        if not upload_folder:
            upload_folder = app.config.get('UPLOAD_FOLDER', 'media/prepare_chat_files')

        # Ensure the directory exists
        os.makedirs(upload_folder, exist_ok=True)

        # Construct the file name
        file_name = f"{user_id}_{time}.{file_extension}"
        file_path = os.path.join(upload_folder, file_name)

        # Write the binary data to the file
        with open(file_path, 'wb') as file:
            file.write(file_content)

        return file_path

    except Exception as e:
        return str(e)