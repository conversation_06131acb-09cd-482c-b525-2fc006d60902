from datetime import datetime, <PERSON><PERSON><PERSON>

def has_logged_in_today(log_data):
    today = datetime.utcnow().date()
    return any(
        isinstance(log.get("date_time"), str) and
        datetime.fromisoformat(log["date_time"]).date() == today
        for log in log_data
    )

def has_seven_day_streak(log_data):
    unique_dates = sorted(set(
        datetime.fromisoformat(log["date_time"]).date()
        for log in log_data
        if isinstance(log.get("date_time"), str)
    ))

    for i in range(len(unique_dates) - 6):
        window = unique_dates[i:i+7]
        if (window[-1] - window[0]).days == 6:
            expected_dates = {window[0] + timedelta(days=j) for j in range(7)}
            if set(window) == expected_dates:
                return True

    return False
