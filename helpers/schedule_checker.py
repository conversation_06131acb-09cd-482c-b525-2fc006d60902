from datetime import datetime, timedelta
from dateutil import parser
import pytz

def is_within_5_minutes(date_str):
    try:
        # Check if the date string is in "DD-MM-YYYY" format
        if "-" in date_str and len(date_str.split("-")[0]) == 2:
            day, month, year = date_str.split("-")
            reformatted_date_str = f"{year}-{month}-{day}"
        else:
            reformatted_date_str = date_str
        
        # Parse the reformatted date string into a datetime object
        input_time = parser.parse(reformatted_date_str, default=datetime.now())
        
        # Remove timezone information if present
        if input_time.tzinfo is not None:
            input_time = input_time.replace(tzinfo=None)
        
        # Get the current time
        now = datetime.now()
        
        # Calculate the time difference
        time_difference = abs(now - input_time)
        
        # Check if the time difference is less than or equal to 5 minutes
        return time_difference <= timedelta(minutes=5)
        
    except (ValueError, OverflowError):
        # Handle cases where the date string can't be parsed
        print(f"Invalid date string format: {date_str}")
        return False

def is_within_15_minutes(scheduled_time):
    """
    Returns True if the scheduled_time (in local time) is within ±12 minutes of UTC now.
    
    Args:
        scheduled_time (datetime or str): The scheduled time to check. Assumed to be in local time.
        
    Returns:
        bool: True if scheduled_time is within ±12 minutes of UTC now.
    """
    try:
        # Parse string to datetime if needed
        if isinstance(scheduled_time, str):
            try:
                scheduled_time = datetime.strptime(scheduled_time, "%Y-%m-%d %H:%M:%S.%f")
            except ValueError:
                scheduled_time = datetime.strptime(scheduled_time, "%Y-%m-%d %H:%M:%S")
        
        # Assume scheduled_time is in local timezone and convert it to UTC
        local_tz = pytz.timezone("Asia/Kolkata")  # Change this to your actual timezone
        scheduled_time = local_tz.localize(scheduled_time).astimezone(pytz.utc)

        # Get current UTC time
        now_utc = datetime.utcnow().replace(tzinfo=pytz.utc)

        delta = (scheduled_time - now_utc).total_seconds()

        return -720 <= delta <= 720  # ±12 minutes
    except Exception as e:
        print(f"Error in is_within_12_minutes: {str(e)}")
        return False