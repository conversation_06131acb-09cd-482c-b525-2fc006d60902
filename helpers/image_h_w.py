# import requests
# from PIL import Image
# from io import BytesIO

# def get_image_dimensions(url):
#     try:
#         response = requests.get(url)
#         if response.status_code == 200:
#             image = Image.open(BytesIO(response.content))
#             return image.width, image.height
#         else:
#             print(f"Failed to fetch image: {response.status_code}")
#             return 0, 0
#     except Exception as e:
#         print(f"Error fetching image dimensions for {url} --> {e}")
#         return 0, 0


import requests
from PIL import Image
from io import BytesIO
import cv2
import numpy as np
import os

def get_image_dimensions(url):
    try:
        response = requests.get(url)

        if response.status_code != 200:
            print(f"Failed to fetch file: {response.status_code}")
            return 0, 0

        file_ext = os.path.splitext(url)[1].lower()
        image_extensions = [
                    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif',
                    '.webp', '.svg', '.ico', '.heif', '.heic', '.raw', '.nef',
                    '.cr2', '.orf', '.arw', '.dng'
                ]
        video_extensions = [
                    '.mp4', '.mkv', '.mov', '.avi', '.flv', '.wmv', '.webm',
                    '.m4v', '.mpg', '.mpeg', '.3gp', '.3g2', '.mts', '.m2ts',
                    '.ts', '.ogv', '.rm', '.rmvb'
                ]
        
        if file_ext in image_extensions:
            image = Image.open(BytesIO(response.content))
            return image.width, image.height

        elif file_ext in video_extensions:
            temp_file = "/tmp/temp_video" + file_ext
            with open(temp_file, 'wb') as f:
                f.write(response.content)

            video = cv2.VideoCapture(temp_file)
            if not video.isOpened():
                print("Failed to open video")
                return 0, 0

            width = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
            video.release()
            return width, height

        else:
            print("Unsupported file type")
            return 0, 0

    except Exception as e:
        print(f"Error fetching dimensions for {url} --> {e}")
        return 0, 0