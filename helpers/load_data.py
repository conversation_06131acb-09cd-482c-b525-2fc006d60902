import numpy as np
import pandas as pd
from Authentication.models import Country, State, City , Industry
from Project.models import PostIndustry

def process_and_store_data(countries_file,states_file,cities_file,category_file,post_industry):
    # Load CSV files using pandas

    countries_df = pd.read_csv(countries_file, usecols=['id', 'name'])
    states_df = pd.read_csv(states_file, usecols=['id', 'name', 'country_id'])
    cities_df = pd.read_csv(cities_file, usecols=['id', 'name', 'state_id'])
    categories_df = pd.read_csv(category_file, usecols=['name'])
    post_industry_df = pd.read_csv(post_industry, usecols=['name'])

    # Convert to NumPy arrays
    countries_data = countries_df.to_numpy()
    states_data = states_df.to_numpy()
    cities_data = cities_df.to_numpy()
    categories_data = categories_df.to_numpy()
    post_industry_data = post_industry_df.to_numpy()

    # # Store in Django models
    countries_bulk = [Country(country_id=int(row[0]), name=row[1]) for row in countries_data]
    states_bulk = [State(state_id=int(row[0]), name=row[1], country_id=int(row[2])) for row in states_data]
    cities_bulk = [City(city_id=int(row[0]), name=row[1], state_id=int(row[2])) for row in cities_data]
    categories_bulk = [Industry(name=row[0]) for row in categories_data]
    post_industry_bulk = [PostIndustry(name=row[0]) for row in post_industry_data]
    
    # Bulk insert into database
    Country.objects.bulk_create(countries_bulk, ignore_conflicts=True)
    State.objects.bulk_create(states_bulk, ignore_conflicts=True)
    City.objects.bulk_create(cities_bulk, ignore_conflicts=True)
    Industry.objects.bulk_create(categories_bulk, ignore_conflicts=True)
    PostIndustry.objects.bulk_create(post_industry_bulk,ignore_conflicts=True)

    return "Data successfully imported!"