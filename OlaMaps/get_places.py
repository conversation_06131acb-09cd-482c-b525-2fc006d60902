import requests

API_KEY = "AMtuoWf1gR5gsWZnpEvA1DLO6ic71006H7izgU1e"

def search_places(search_text):
    url = f"https://api.olamaps.io/places/v1/autocomplete?input={search_text}&api_key={API_KEY}"

    payload = {}
    headers = {
    'accept': 'application/json'
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    if response.status_code == 200:
        data = response.json()
        output = []
        for single_data in data['predictions']:
            append_obj = {
                "suggested_place":single_data["description"],
                "lat":single_data["geometry"]["location"]["lat"],
                "lng":single_data["geometry"]["location"]["lng"],
            }
            output.append(append_obj)
        return True , output
    else:
        return False,output

def nearby_places(lat,long):
    url = f"https://api.olamaps.io/places/v1/nearbysearch"
    params = {
        "location":f"{lat},{long}",
        "rankby":"popular",
        "api_key":API_KEY
    }
    response = requests.get(url=url,params=params)
    if response.status_code == 200:
        response_data = response.json()
        data_list = []
        for data in response_data['predictions']:
            append_obj = {
                "suggested_place":data['description'],
                "lat":"",
                "lng":"",
            }

            data_list.append(append_obj)

        return True,data_list
    else:
        return False,[]