import ffmpeg
import subprocess
import subprocess
import ffmpeg
import os

def get_resized_dimensions(original_width, original_height, max_width=None, max_height=None):
    aspect_ratio = original_width / original_height

    if max_width and max_height:
        if original_width > max_width or original_height > max_height:
            if max_width / aspect_ratio <= max_height:
                new_width = max_width
                new_height = int(max_width / aspect_ratio)
            else:
                new_height = max_height
                new_width = int(max_height * aspect_ratio)
        else:
            return original_width, original_height
    elif max_width:
        new_width = max_width
        new_height = int(max_width / aspect_ratio)
    elif max_height:
        new_height = max_height
        new_width = int(max_height * aspect_ratio)
    else:
        return original_width, original_height

    return new_width, new_height

def resize_image(input_image_path, output_image_path, max_width=None, max_height=None, quality=3):
    if not os.path.exists(input_image_path):
        raise FileNotFoundError(f"Input file not found: {input_image_path}")

    probe = ffmpeg.probe(input_image_path)
    video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
    
    if video_stream is None:
        raise ValueError("No video stream found in the input file.")
    
    original_width = int(video_stream['width'])
    original_height = int(video_stream['height'])
    
    new_width, new_height = get_resized_dimensions(original_width, original_height, max_width, max_height)

    # Ensure the output is in JPEG format
    output_image_path = os.path.splitext(output_image_path)[0] + ".jpeg"

    command = [
        'ffmpeg',
        '-y',  # Automatically overwrite output files
        '-i', input_image_path,
        '-vf', f'scale={new_width}:{new_height}',
        '-q:v', str(quality),  # Set quality for images
        '-f', 'image2',  # Force image format
        output_image_path
    ]

    try:
        subprocess.run(command, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error during image resizing: {e.stderr.decode()}")


def get_video_dimensions(input_file_path, max_width=None, max_height=None):
    if not os.path.exists(input_file_path):
        raise FileNotFoundError(f"Input file not found: {input_file_path}")

    probe = ffmpeg.probe(input_file_path)
    video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
    
    if video_stream is None:
        raise ValueError("No video stream found in the input file.")

    original_width = int(video_stream['width'])
    original_height = int(video_stream['height'])
    
    return get_resized_dimensions(original_width, original_height, max_width, max_height)

def get_video_duration(input_file_path):
    # Get the video duration in seconds using FFmpeg
    command = [
        'ffprobe',
        '-v', 'error',
        '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        input_file_path
    ]
    result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    duration = float(result.stdout.decode().strip())
    return duration

def calculate_bitrate(target_size_mb, duration_seconds):
    # Calculate target bitrate based on desired file size and video duration
    return (target_size_mb * 8192) / duration_seconds  # Convert MB to kilobits

def adjust_dimensions(width, height):
    # Ensure both width and height are divisible by 2
    if width % 2 != 0:
        width -= 1
    if height % 2 != 0:
        height -= 1
    return width, height



# def compress_video(input_file_path, output_file_path, max_width=None, max_height=None, quality=23, target_size_mb=30):
#     # Get original dimensions
#     original_width, original_height = get_video_dimensions(input_file_path)

#     # Get the video duration
#     duration_seconds = get_video_duration(input_file_path)

#     # Calculate the target bitrate for compression
#     target_bitrate = calculate_bitrate(target_size_mb, duration_seconds)
#     target_bitrate_str = f'{int(target_bitrate)}k'

#     # Build the scale filter preserving aspect ratio
#     if max_width and max_height:
#         scale_filter = f"scale='min(iw,{max_width})':'min(ih,{max_height})':force_original_aspect_ratio=decrease"
#     elif max_width:
#         scale_filter = f"scale='min(iw,{max_width})':-2"
#     elif max_height:
#         scale_filter = f"scale=-2:'min(ih,{max_height})'"
#     else:
#         scale_filter = "scale=iw:ih"  # No scaling at all

#     # Pad to ensure even dimensions (required by libx264)
#     scale_filter += ",pad=ceil(iw/2)*2:ceil(ih/2)*2"

#     command = [
#         'ffmpeg',
#         '-y',
#         '-i', input_file_path,
#         '-vf', scale_filter,
#         '-vcodec', 'libx264',
#         '-b:v', target_bitrate_str,
#         '-preset', 'slow',
#         '-b:a', '128k',
#         '-maxrate', target_bitrate_str,
#         '-f', 'mp4'
#         '-bufsize', '1000k',
#         '-crf', str(quality),
#         output_file_path
#     ]

#     try:
#         subprocess.run(command, check=True, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
#         print(f"Compression completed successfully. Output saved to {output_file_path}.")
#     except subprocess.CalledProcessError as e:
#         error_output = e.stderr.decode() if e.stderr else "No error output"
#         print(f"Error during video compression: {error_output}")



def compress_video(input_file_path, output_file_path, max_width=None, max_height=None, quality=23, target_size_mb=30):
    # Ensure output file has .mp4 extension (only change extension, keep path)
    if not output_file_path.lower().endswith('.mp4'):
        base_path = output_file_path.rsplit('.', 1)[0] if '.' in output_file_path else output_file_path
        output_file_path = base_path + '.mp4'
    # Get original dimensions
    original_width, original_height = get_video_dimensions(input_file_path)

    # Get the video duration
    duration_seconds = get_video_duration(input_file_path)

    # Calculate the target bitrate for compression
    target_bitrate = calculate_bitrate(target_size_mb, duration_seconds)
    target_bitrate_str = f'{int(target_bitrate)}k'

    # Build the scale filter preserving aspect ratio
    if max_width and max_height:
        scale_filter = f"scale='min(iw,{max_width})':'min(ih,{max_height})':force_original_aspect_ratio=decrease"
    elif max_width:
        scale_filter = f"scale='min(iw,{max_width})':-2"
    elif max_height:
        scale_filter = f"scale=-2:'min(ih,{max_height})'"
    else:
        scale_filter = "scale=iw:ih"  # No scaling at all

    # Pad to ensure even dimensions (required by libx264)
    scale_filter += ",pad=ceil(iw/2)*2:ceil(ih/2)*2"

    command = [
        'ffmpeg',
        '-y',
        '-i', input_file_path,
        '-vf', scale_filter,
        '-vcodec', 'libx264',
        '-profile:v', 'baseline',  # Added: Use baseline profile for maximum compatibility
        '-level', '3.0',           # Added: H.264 level 3.0 for wide device support
        '-pix_fmt', 'yuv420p',     # Added: Ensure YUV 4:2:0 pixel format for compatibility
        '-b:v', target_bitrate_str,
        '-preset', 'slow',
        '-c:a', 'aac',
        '-b:a', '128k',
        '-ar', '44100',            # Added: Standard audio sample rate
        '-ac', '2',                # Added: Stereo audio channels
        '-maxrate', target_bitrate_str,
        '-bufsize', '1000k',
        '-crf', str(quality),
        '-movflags', '+faststart', # Added: Enable fast start for web streaming
        '-f', 'mp4',
        output_file_path
    ]

    try:
        subprocess.run(command, check=True, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
        print(f"Compression completed successfully. Output saved to {output_file_path}.")
    except subprocess.CalledProcessError as e:
        error_output = e.stderr.decode() if e.stderr else "No error output"
        print(f"Error during video compression: {error_output}")